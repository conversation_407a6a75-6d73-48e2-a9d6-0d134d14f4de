import logging
import logging.config
import configparser
from functools import wraps
from urllib.parse import quote_plus
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError

# 配置日志
logging.config.fileConfig('conf/logging.conf')
logger = logging.getLogger('dbutil')

# 读取配置
db_config = configparser.ConfigParser()
db_config.read('conf/db.ini')

# 构建数据库连接URL
# 对用户名和密码进行URL编码以处理特殊字符
user = quote_plus(db_config.get('mysql', 'user'))
password = quote_plus(db_config.get('mysql', 'password'))
host = db_config.get('mysql', 'host')
database = db_config.get('mysql', 'database')
db_url = f"mysql+mysqlconnector://{user}:{password}@{host}/{database}"

# 创建引擎和连接池
try:
    engine = create_engine(
        db_url,
        pool_size=db_config.getint('mysql', 'pool_size'),
        max_overflow=10,
        pool_recycle=3600,  # 连接回收时间(秒)
        pool_pre_ping=True,  # 自动检测连接是否有效
        pool_timeout=30,     # 获取连接的超时时间
        echo=False           # 设置为True可以查看SQL语句
    )
    logger.info("Database engine created successfully")
except Exception as e:
    logger.error(f"Error creating database engine: {e}")
    engine = None

def get_connection():
    """获取数据库连接"""
    if engine is None:
        logger.error("Database engine is not initialized")
        return None

    try:
        conn = engine.connect()
        logger.debug("Connection retrieved from pool")
        return conn
    except SQLAlchemyError as e:
        logger.error(f"Error getting connection from pool: {e}")
        return None

def db_operation(func):
    """数据库操作装饰器，处理连接获取和异常"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        conn = get_connection()
        if conn is None:
            logger.error(f"Cannot execute {func.__name__}: No database connection available")
            return None

        try:
            result = func(conn, *args, **kwargs)
            return result
        except SQLAlchemyError as e:
            logger.error(f"Database error in {func.__name__}: {e}")
            return None
        finally:
            if conn:
                conn.close()
                logger.debug("Connection returned to pool")

    return wrapper

@db_operation
def get_bras_list(conn):
    """获取所有BRAS设备列表"""
    query = text("SELECT bras_ip, bras_secret, bras_model, bras_vendor, bras_area FROM bras")
    result = conn.execute(query)
    return [row._asdict() for row in result]

@db_operation
def get_bras_by_ip(conn, ip):
    """根据IP获取BRAS设备信息"""
    query = text("""
        SELECT
            bras_ip,
            bras_secret,
            bras_model,
            bras_vendor,
            bras_area
        FROM bras
        WHERE bras_ip = :ip
    """)
    result = conn.execute(query, {"ip": ip})
    row = result.fetchone()
    return row._asdict() if row else None

@db_operation
def get_user_info(conn, user_name, user_domain=None):
    """获取用户信息"""
    params = {"user_name": user_name}

    if user_domain:
        conditions = "WHERE user_name = :user_name AND user_domain = :user_domain"
        params["user_domain"] = user_domain
    else:
        conditions = "WHERE user_name = :user_name AND user_domain = ''"

    query = text(f"""
    SELECT
        user_name,
        user_password,
        user_password_type,
        user_domain,
        user_business_type,
        user_area,
        user_line_bind_type,
        user_line_bind_info,
        user_bind_nas,
        user_bind_ip,
        user_allow_onlinenums,
        user_status,
        user_pause_datetime,
        user_open_datetime,
        user_open_operator,
        user_expire_datetime,
        user_modify_datetime,
        user_modify_operator,
        user_down_bandwidth,
        user_up_bandwidth,
        user_ip_type,
        user_ipv6_prefix,
        user_ipv6_interfaceid,
        user_bindwidth_template_id,
        user_product_type,
        user_allow_start_time,
        user_allow_stop_time,
        user_primary_username,
        user_session_timemout
    FROM user
    {conditions}
    """)

    result = conn.execute(query, params)
    row = result.fetchone()
    return row._asdict() if row else None

@db_operation
def insert_authrecord(conn, record):
    """插入认证记录"""
    query = text("""
        INSERT INTO authrecord (
            user_name, user_domain, user_business_type, auth_date,
            auth_result_code, bras_ip, bras_port, bras_port_type, line_info,
            mac, radius_server, dail_user_name
        ) VALUES (
            :user_name, :user_domain, :user_business_type, :auth_date,
            :auth_result_code, :bras_ip, :bras_port, :bras_port_type, :line_info,
            :mac, :radius_server, :dail_user_name
        )
    """)

    # 格式化日期时间
    if isinstance(record['auth_date'], str):
        auth_date = record['auth_date']
    else:
        auth_date = record['auth_date'].strftime('%Y-%m-%d %H:%M:%S')

    params = {
        'user_name': record['user_name'],
        'user_domain': record['user_domain'],
        'user_business_type': record['user_business_type'],
        'auth_date': auth_date,
        'auth_result_code': record['auth_result_code'],
        'bras_ip': record['bras_ip'],
        'bras_port': record['bras_port'],
        'bras_port_type': record['bras_port_type'],
        'line_info': record['line_info'],
        'mac': record['mac'],
        'radius_server': record['radius_server'],
        'dail_user_name': record['dail_user_name']
    }

    result = conn.execute(query, params)
    conn.commit()
    logger.info("Authentication result inserted into authrecord table")
    return result.rowcount > 0

@db_operation
def insert_onlinerecord(conn, record):
    """插入在线记录"""
    query = text("""
        INSERT INTO onlinerecord (
            user_name, user_domain, user_business_type, online_time, line_info,
            mac, bras_ip, bras_port, bras_port_type, session_id, user_area,
            bras_area, client_type, packet_process_time, dail_user_name,
            user_nat_framedip, user_nat_beginport, user_nat_endport,
            user_framedip, user_framedipv6, user_delegated_ipv6prefix,
            user_ipv4_outoctets, user_ipv4_inoctets, user_ipv4_outpackets,
            user_ipv4_inpackets, user_ipv6_outoctets, user_ipv6_inoctets,
            user_ipv6_outpackets, user_ipv6_inpackets, packet_type, radius_server
        ) VALUES (
            :user_name, :user_domain, :user_business_type, :online_time, :line_info,
            :mac, :bras_ip, :bras_port, :bras_port_type, :session_id, :user_area,
            :bras_area, :client_type, :packet_process_time, :dail_user_name,
            :user_nat_framedip, :user_nat_beginport, :user_nat_endport,
            :user_framedip, :user_framedipv6, :user_delegated_ipv6prefix,
            :user_ipv4_outoctets, :user_ipv4_inoctets, :user_ipv4_outpackets,
            :user_ipv4_inpackets, :user_ipv6_outoctets, :user_ipv6_inoctets,
            :user_ipv6_outpackets, :user_ipv6_inpackets, :packet_type, :radius_server
        )
    """)

    # 格式化日期时间
    if isinstance(record['online_time'], str):
        online_time = record['online_time']
    else:
        online_time = record['online_time'].strftime('%Y-%m-%d %H:%M:%S')

    if isinstance(record['packet_process_time'], str):
        packet_process_time = record['packet_process_time']
    else:
        packet_process_time = record['packet_process_time'].strftime('%Y-%m-%d %H:%M:%S')

    # 准备参数
    params = {
        'user_name': record['user_name'],
        'user_domain': record['user_domain'],
        'user_business_type': record['user_business_type'],
        'online_time': online_time,
        'line_info': record['line_info'],
        'mac': record['mac'],
        'bras_ip': record['bras_ip'],
        'bras_port': record.get('bras_port', 0),
        'bras_port_type': record['bras_port_type'],
        'session_id': record['session_id'],
        'user_area': record['user_area'],
        'bras_area': record['bras_area'],
        'client_type': record['client_type'],
        'packet_process_time': packet_process_time,
        'dail_user_name': record['dail_user_name'],
        'user_nat_framedip': record.get('user_nat_framedip', ''),
        'user_nat_beginport': record.get('user_nat_beginport', 0),
        'user_nat_endport': record.get('user_nat_endport', 0),
        'user_framedip': record.get('user_framedip', ''),
        'user_framedipv6': record.get('user_framedipv6', ''),
        'user_delegated_ipv6prefix': record.get('user_delegated_ipv6prefix', ''),
        'user_ipv4_outoctets': record.get('user_ipv4_outoctets', 0),
        'user_ipv4_inoctets': record.get('user_ipv4_inoctets', 0),
        'user_ipv4_outpackets': record.get('user_ipv4_outpackets', 0),
        'user_ipv4_inpackets': record.get('user_ipv4_inpackets', 0),
        'user_ipv6_outoctets': record.get('user_ipv6_outoctets', 0),
        'user_ipv6_inoctets': record.get('user_ipv6_inoctets', 0),
        'user_ipv6_outpackets': record.get('user_ipv6_outpackets', 0),
        'user_ipv6_inpackets': record.get('user_ipv6_inpackets', 0),
        'packet_type': record.get('packet_type', ''),
        'radius_server': record['radius_server']
    }

    result = conn.execute(query, params)
    conn.commit()
    logger.info("Online record inserted into onlinerecord table")
    return result.rowcount > 0


@db_operation
def insert_detail(conn, record):
    """插入详细记录"""
    query = text("""
        INSERT INTO detail (
            user_name, user_domain, user_business_type, online_time, offline_time,
            duration, user_nat_framedip, user_nat_beginport, user_nat_endport,
            user_framedip, user_framedipv6, user_delegated_ipv6prefix, user_ipv4_outoctets,
            user_ipv4_inoctets, user_ipv4_outpackets, user_ipv4_inpackets, user_ipv6_outoctets,
            user_ipv6_inoctets, user_ipv6_outpackets, user_ipv6_inpackets, line_info,
            mac, bras_ip, bras_port, bras_port_type, session_id, user_area,
            bras_area, client_type, packet_process_time, dail_user_name, down_reason,
            radius_server
        ) VALUES (
            :user_name, :user_domain, :user_business_type, :online_time, :offline_time,
            :duration, :user_nat_framedip, :user_nat_beginport, :user_nat_endport,
            :user_framedip, :user_framedipv6, :user_delegated_ipv6prefix, :user_ipv4_outoctets,
            :user_ipv4_inoctets, :user_ipv4_outpackets, :user_ipv4_inpackets, :user_ipv6_outoctets,
            :user_ipv6_inoctets, :user_ipv6_outpackets, :user_ipv6_inpackets, :line_info,
            :mac, :bras_ip, :bras_port, :bras_port_type, :session_id, :user_area,
            :bras_area, :client_type, :packet_process_time, :dail_user_name, :down_reason,
            :radius_server
        )
    """)

    # 格式化日期时间
    if isinstance(record['online_time'], str):
        online_time = record['online_time']
    else:
        online_time = record['online_time'].strftime('%Y-%m-%d %H:%M:%S')

    if isinstance(record['offline_time'], str):
        offline_time = record['offline_time']
    else:
        offline_time = record['offline_time'].strftime('%Y-%m-%d %H:%M:%S')

    if isinstance(record['packet_process_time'], str):
        packet_process_time = record['packet_process_time']
    else:
        packet_process_time = record['packet_process_time'].strftime('%Y-%m-%d %H:%M:%S')

    params = {
        'user_name': record['user_name'],
        'user_domain': record['user_domain'],
        'user_business_type': record['user_business_type'],
        'online_time': online_time,
        'offline_time': offline_time,
        'duration': record['duration'],
        'user_nat_framedip': record['user_nat_framedip'],
        'user_nat_beginport': record['user_nat_beginport'],
        'user_nat_endport': record['user_nat_endport'],
        'user_framedip': record['user_framedip'],
        'user_framedipv6': record['user_framedipv6'],
        'user_delegated_ipv6prefix': record['user_delegated_ipv6prefix'],
        'user_ipv4_outoctets': record['user_ipv4_outoctets'],
        'user_ipv4_inoctets': record['user_ipv4_inoctets'],
        'user_ipv4_outpackets': record['user_ipv4_outpackets'],
        'user_ipv4_inpackets': record['user_ipv4_inpackets'],
        'user_ipv6_outoctets': record['user_ipv6_outoctets'],
        'user_ipv6_inoctets': record['user_ipv6_inoctets'],
        'user_ipv6_outpackets': record['user_ipv6_outpackets'],
        'user_ipv6_inpackets': record['user_ipv6_inpackets'],
        'line_info': record['line_info'],
        'mac': record['mac'],
        'bras_ip': record['bras_ip'],
        'bras_port': record['bras_port'],
        'bras_port_type': record['bras_port_type'],
        'session_id': record['session_id'],
        'user_area': record['user_area'],
        'bras_area': record['bras_area'],
        'client_type': record['client_type'],
        'packet_process_time': packet_process_time,
        'dail_user_name': record['dail_user_name'],
        'down_reason': record['down_reason'],
        'radius_server': record['radius_server']
    }

    result = conn.execute(query, params)
    conn.commit()
    logger.info("Detail record inserted into detail table")
    return result.rowcount > 0


@db_operation
def update_onlinerecord_by_sessionid(conn, session_id, update_record, insert_record, acct_update_flag):
    """根据session_id更新在线记录"""
    # 检查记录是否存在
    check_query = text("SELECT COUNT(session_id) FROM onlinerecord WHERE session_id = :session_id")
    result = conn.execute(check_query, {"session_id": session_id})
    record_exists = result.fetchone()[0] > 0

    if record_exists:
        # 构建更新查询
        update_fields = []
        params = {"session_id": session_id}

        for field in update_record.keys():
            update_fields.append(f"{field} = :{field}")
            params[field] = update_record[field]

        update_query = text(f"UPDATE onlinerecord SET {', '.join(update_fields)} WHERE session_id = :session_id")

        result = conn.execute(update_query, params)
        conn.commit()

        if result.rowcount > 0:
            logger.info("Record updated in onlinerecord table")
        else:
            logger.info("No record found for the given session_id")

        return result.rowcount > 0
    else:
        if not acct_update_flag or (acct_update_flag and insert_record.get('duration', 0) > 5):
            # 如果不存在记录，则插入数据到onlinerecord表
            logger.info(f"No record found for session_id {session_id}, inserting new record into onlinerecord.")
            return insert_onlinerecord(insert_record)
        else:
            logger.info(f"No record found for session_id {session_id}, but address update packet before offline do not insert into onlinerecord table")
            return False


@db_operation
def delete_onlinerecord_by_sessionid(conn, session_id):
    """根据session_id删除在线记录"""
    query = text("DELETE FROM onlinerecord WHERE session_id = :session_id")
    result = conn.execute(query, {"session_id": session_id})
    conn.commit()

    if result.rowcount > 0:
        logger.info(f"Record with session_id {session_id} deleted from onlinerecord table")
    else:
        logger.info(f"No record found for the given session_id {session_id}")

    return result.rowcount > 0

@db_operation
def get_online_count_by_user(conn, user_name, user_domain, user_business_type):
    """获取用户在线数量"""
    query = text("""
        SELECT COUNT(*) as count FROM onlinerecord
        WHERE user_name = :user_name AND user_domain = :user_domain AND user_business_type = :user_business_type
    """)
    result = conn.execute(query, {
        "user_name": user_name,
        "user_domain": user_domain,
        "user_business_type": user_business_type
    })
    row = result.fetchone()
    return row[0] if row else 0

@db_operation
def get_vpdn_list(conn):
    """获取所有VPDN域名列表"""
    query = text("SELECT vpdn_domain, vpdn_name, vpdn_areano, description FROM vpdn")
    result = conn.execute(query)
    return [row._asdict() for row in result]

@db_operation
def check_domain_exists(conn, domain):
    """检查域名是否存在于VPDN表中"""
    query = text("SELECT COUNT(*) as count FROM vpdn WHERE vpdn_domain = :domain")
    result = conn.execute(query, {"domain": domain})
    row = result.fetchone()
    return row[0] > 0 if row else False
