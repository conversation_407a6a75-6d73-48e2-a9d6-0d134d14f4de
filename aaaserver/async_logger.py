"""
异步协程安全的日志系统
支持请求追踪和结构化日志
"""

import logging
import asyncio
import uuid
import time
import json
from datetime import datetime
from contextvars import ContextVar
from typing import Optional, Dict, Any

# 上下文变量，用于在协程中传递请求ID
request_id_var: ContextVar[Optional[str]] = ContextVar('request_id', default=None)
session_info_var: ContextVar[Optional[Dict]] = ContextVar('session_info', default=None)

class AsyncRadiusLogger:
    """异步RADIUS日志器"""

    def __init__(self, name: str = 'async_radius'):
        self.logger = logging.getLogger(name)
        self._setup_logger()

    def _setup_logger(self):
        """设置日志器"""
        # 如果已经有处理器，不重复设置
        if self.logger.handlers:
            return

        # 创建格式化器
        formatter = AsyncRadiusFormatter()

        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(logging.INFO)

        # 文件处理器
        file_handler = logging.FileHandler('logs/async_radius.log')
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.DEBUG)

        # 添加处理器
        self.logger.addHandler(console_handler)
        self.logger.addHandler(file_handler)
        self.logger.setLevel(logging.DEBUG)

    def start_request(self, client_ip: str, user_name: str = None, request_type: str = 'auth') -> str:
        """开始一个新的请求，返回请求ID"""
        req_id = str(uuid.uuid4())[:8]  # 使用短UUID
        request_id_var.set(req_id)

        session_info = {
            'client_ip': client_ip,
            'user_name': user_name,
            'request_type': request_type,
            'start_time': time.time()
        }
        session_info_var.set(session_info)

        self.info(f"🚀 Request started", extra={
            'event': 'request_start',
            'client_ip': client_ip,
            'user_name': user_name,
            'request_type': request_type
        })

        return req_id

    def end_request(self, result: str = 'completed'):
        """结束请求"""
        session_info = session_info_var.get()
        if session_info:
            duration = time.time() - session_info['start_time']
            self.info(f"✅ Request {result}", extra={
                'event': 'request_end',
                'result': result,
                'duration_ms': round(duration * 1000, 2)
            })

        # 清理上下文
        request_id_var.set(None)
        session_info_var.set(None)

    def log_packet_attributes(self, pkt, title: str = "Packet Attributes"):
        """记录RADIUS包属性"""
        self.info("**************************************** [Attributes] ****************************************")

        # 按照特定顺序显示所有属性
        for attr in pkt.keys():
            try:
                value = pkt[attr]
                self.info(f"{attr}: {value}")
            except Exception as e:
                self.info(f"{attr}: <error reading value: {e}>")

    def log_auth_step(self, step: str, result: str, details: Dict[str, Any] = None):
        """记录认证步骤"""
        emoji_map = {
            'domain_check': '🌐',
            'user_check': '👤',
            'password_check': '🔐',
            'status_check': '📊',
            'online_limit_check': '📈',
            'authorization': '✅'
        }

        emoji = emoji_map.get(step, '🔍')
        self.info(f"{emoji} {step}: {result}", extra={
            'event': 'auth_step',
            'step': step,
            'result': result,
            'details': details or {}
        })

    def log_database_operation(self, operation: str, table: str, duration_ms: float = None):
        """记录数据库操作"""
        msg = f"💾 DB {operation} on {table}"
        if duration_ms is not None:
            msg += f" ({duration_ms:.2f}ms)"

        self.debug(msg, extra={
            'event': 'database_operation',
            'operation': operation,
            'table': table,
            'duration_ms': duration_ms
        })

    def log_performance_stats(self, stats: Dict[str, Any]):
        """记录性能统计"""
        self.info(f"📊 Performance Stats", extra={
            'event': 'performance_stats',
            'stats': stats
        })

    # 标准日志方法
    def debug(self, msg: str, extra: Dict[str, Any] = None):
        self._log(logging.DEBUG, msg, extra)

    def info(self, msg: str, extra: Dict[str, Any] = None):
        self._log(logging.INFO, msg, extra)

    def warning(self, msg: str, extra: Dict[str, Any] = None):
        self._log(logging.WARNING, msg, extra)

    def error(self, msg: str, extra: Dict[str, Any] = None):
        self._log(logging.ERROR, msg, extra)

    def _log(self, level: int, msg: str, extra: Dict[str, Any] = None):
        """内部日志方法"""
        # 获取当前上下文信息
        req_id = request_id_var.get()
        session_info = session_info_var.get()

        # 构建额外信息
        log_extra = {
            'request_id': req_id,
            'coroutine_id': id(asyncio.current_task()) if asyncio.current_task() else None
        }

        if session_info:
            log_extra.update({
                'client_ip': session_info.get('client_ip'),
                'user_name': session_info.get('user_name'),
                'request_type': session_info.get('request_type')
            })

        if extra:
            log_extra.update(extra)

        self.logger.log(level, msg, extra=log_extra)


class AsyncRadiusFormatter(logging.Formatter):
    """异步RADIUS日志格式化器"""

    def __init__(self):
        super().__init__()

    def format(self, record):
        # 基础时间格式
        timestamp = datetime.fromtimestamp(record.created).strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]

        # 获取请求ID
        req_id = getattr(record, 'request_id', None)
        req_id_str = f"[{req_id}]" if req_id else "[----]"

        # 获取用户信息
        user_name = getattr(record, 'user_name', None)
        user_str = f"[{user_name}]" if user_name else ""

        # 获取客户端IP
        client_ip = getattr(record, 'client_ip', None)
        client_str = f"[{client_ip}]" if client_ip else ""

        # 构建基础格式
        base_format = f"[{timestamp}] [{record.levelname}] {req_id_str}{user_str}{client_str} {record.getMessage()}"

        # 如果有结构化数据，添加JSON格式
        if hasattr(record, 'event'):
            structured_data = {
                'event': record.event,
                'timestamp': timestamp,
                'level': record.levelname,
                'request_id': req_id,
                'message': record.getMessage()
            }

            # 添加其他字段
            for key, value in record.__dict__.items():
                if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                              'filename', 'module', 'lineno', 'funcName', 'created',
                              'msecs', 'relativeCreated', 'thread', 'threadName',
                              'processName', 'process', 'message', 'exc_info', 'exc_text',
                              'stack_info', 'request_id', 'event']:
                    if not key.startswith('_'):
                        structured_data[key] = value

            # 在调试模式下添加JSON
            if record.levelno <= logging.DEBUG:
                base_format += f" | JSON: {json.dumps(structured_data, ensure_ascii=False)}"

        return base_format


# 全局日志器实例
async_logger = AsyncRadiusLogger()

# 便捷函数
def start_request(client_ip: str, user_name: str = None, request_type: str = 'auth') -> str:
    """开始请求追踪"""
    return async_logger.start_request(client_ip, user_name, request_type)

def end_request(result: str = 'completed'):
    """结束请求追踪"""
    async_logger.end_request(result)

def log_packet_attributes(pkt, title: str = "Packet Attributes"):
    """记录包属性"""
    async_logger.log_packet_attributes(pkt, title)

def log_auth_step(step: str, result: str, details: Dict[str, Any] = None):
    """记录认证步骤"""
    async_logger.log_auth_step(step, result, details)

def log_database_operation(operation: str, table: str, duration_ms: float = None):
    """记录数据库操作"""
    async_logger.log_database_operation(operation, table, duration_ms)

def log_performance_stats(stats: Dict[str, Any]):
    """记录性能统计"""
    async_logger.log_performance_stats(stats)

# 标准日志函数
def debug(msg: str, extra: Dict[str, Any] = None):
    async_logger.debug(msg, extra)

def info(msg: str, extra: Dict[str, Any] = None):
    async_logger.info(msg, extra)

def warning(msg: str, extra: Dict[str, Any] = None):
    async_logger.warning(msg, extra)

def error(msg: str, extra: Dict[str, Any] = None):
    async_logger.error(msg, extra)
