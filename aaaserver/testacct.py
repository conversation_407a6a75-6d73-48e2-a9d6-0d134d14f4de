#!/usr/bin/python
from __future__ import print_function

import binascii

from pyrad.client import Client
from pyrad.dictionary import Dictionary
import random
import socket
import sys
import pyrad.packet


def SendPacket(srv, req):
    try:
        srv.SendPacket(req)
    except pyrad.client.Timeout:
        print("RADIUS server does not reply")
        sys.exit(1)
    except socket.error as error:
        print("Network error: " + error[1])
        sys.exit(1)

srv = Client(server="127.0.0.1", secret=b"huaweitest", dict=Dictionary("conf/dictionary"))
print("Sending accounting start packet")
req = srv.CreateAcctPacket(User_Name="wichert")
req["NAS-IP-Address"] = "************"
req["NAS-Port"] = 0
req["NAS-Identifier"] = "trillian"
req["Called-Station-Id"] = "00-04-5F-00-0F-D1"
req["Calling-Station-Id"] = "00-01-24-80-B3-9C"
req["Framed-IP-Address"] = "**********"
req["Acct-Status-Type"] = "Start"
req["Framed-Interface-Id"] = "76b7b3fffeefd8e9"
ipv6_prefix = "2001:db8::/64"
ipv6_addr, prefix_len = ipv6_prefix.split('/')

# 处理IPv6地址部分
ipv6_addr_hex = ipv6_addr.replace(':', '')
if len(ipv6_addr_hex) % 2 != 0:  # 如果长度是奇数
    ipv6_addr_hex = '0' + ipv6_addr_hex  # 在前面补一个'0'
ipv6_addr_bytes = binascii.unhexlify(ipv6_addr_hex)

# 处理前缀长度部分
prefix_len_bytes = bytes([int(prefix_len)])

# 组合IPv6地址和前缀长度
ipv6_prefix_bytes = ipv6_addr_bytes + prefix_len_bytes

req["Framed-IPv6-Prefix"] = ipv6_prefix_bytes

SendPacket(srv, req)

# print("Sending accounting stop packet")
# req["Acct-Status-Type"] = "Stop"
# req["Acct-Input-Octets"] = random.randrange(2**10, 2**30)
# req["Acct-Output-Octets"] = random.randrange(2**10, 2**30)
# req["Acct-Session-Time"] = random.randrange(120, 3600)
# req["Acct-Terminate-Cause"] = random.choice(["User-Request", "Idle-Timeout"])
# SendPacket(srv, req)
