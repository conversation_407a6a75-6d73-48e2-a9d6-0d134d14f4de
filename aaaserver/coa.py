#!/usr/bin/python
from __future__ import print_function
from pyrad.client import Client
from pyrad import dictionary
from pyrad import packet
import sys


def process_coa_request(operation, address, secret,sessionid):
    if operation not in ["coa", "dis"]:
        print ("Invalid operation. Use 'coa' or 'dis'.")
        return

    ATTRIBUTES = {
        "Acct-Session-Id": sessionid
    }
    # ATTRIBUTES["NAS-Identifier"] = nas_identifier

    client = Client(server=address, secret=secret, dict=dictionary.Dictionary("conf/dictionary"))
    client.timeout = 30

    attributes = {k.replace("-", "_"): ATTRIBUTES[k] for k in ATTRIBUTES}

    if operation == "coa":
        request = client.CreateCoAPacket(**attributes)
    elif operation == "dis":
        request = client.CreateCoAPacket(code=packet.DisconnectRequest, **attributes)

    result = client.SendPacket(request)
    print(result)
    print(result.code)
    return result


# 将下面的代码改写为函数
# if len(sys.argv) != 3:
#   print ("usage: coa.py {coa|dis} daemon-1234")
#   sys.exit(1)

# ADDRESS = "***********"
# SECRET = b"tltest"
# ATTRIBUTES = {
#     "Acct-Session-Id": "320f4d601c65-10"
# }
#
# ATTRIBUTES["NAS-Identifier"] = sys.argv[2]

# create coa client
# client = Client(server=ADDRESS, secret=SECRET, dict=dictionary.Dictionary("conf/dictionary"))

# set coa timeout
# client.timeout = 30

# create coa request packet
# attributes = {k.replace("-", "_"): ATTRIBUTES[k] for k in ATTRIBUTES}

# if sys.argv[1] == "coa":
#     # create coa request
#     request = client.CreateCoAPacket(**attributes)
# elif sys.argv[1] == "dis":
#     # create disconnect request
#     request = client.CreateCoAPacket(code=packet.DisconnectRequest, **attributes)
# else:
#   sys.exit(1)
#
# # send request
# result = client.SendPacket(request)
# print(result)
# print(result.code)
