# 使用官方 Python 3.11 精简版镜像
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装 git 和 SSH 服务
RUN apt-get update && apt-get install -y \
    git \
    openssh-server \
    && rm -rf /var/lib/apt/lists/*

# 配置 SSH 服务（使用 echo 命令创建 SSH 密钥对，你可以根据需要自定义）
RUN mkdir /var/run/sshd && \
    echo "root:Docker!" | chpasswd && \
    sed -i 's/#PermitRootLogin prohibit-password/PermitRootLogin yes/' /etc/ssh/sshd_config && \
    sed -i 's/#StrictModes yes/StrictModes no/' /etc/ssh/sshd_config && \
    sed -i 's/#PubkeyAuthentication yes/PubkeyAuthentication yes/' /etc/ssh/sshd_config && \
    sed -i 's/#PasswordAuthentication yes/PasswordAuthentication yes/' /etc/ssh/sshd_config && \
    ssh-keygen -t rsa -f /root/.ssh/id_rsa -N ''

# 复制 Git 仓库访问令牌和 SSH 公钥
ARG SSH_PUBLIC_KEY
ARG GIT_BRANCH
ARG GIT_USER
ARG GIT_EMAIL
ARG GIT_USERNAME
ARG GIT_PASSWORD


# 将 SSH 公钥添加到 authorized_keys
RUN echo "${SSH_PUBLIC_KEY}" >> /root/.ssh/authorized_keys && \
    chmod 600 /root/.ssh/authorized_keys &&\
    git config --global user.name "${GIT_USER}" && \
    git config --global user.email "${GIT_EMAIL}" && \
    git clone -b ${GIT_BRANCH} https://${GIT_USERNAME}:${GIT_PASSWORD}@gitee.com/linshuinew/smartAAA.git /app &&\
    pip install --no-cache-dir -r requirements.txt 

ENV PYTHONPATH=/app

#COPY requirements.txt /app/

# 暴露 SSH 和 RADIUS 端口
EXPOSE 22 1812 1813

# 启动 SSH 服务，并运行 Python 应用
CMD service ssh start && python ./my/server.py
