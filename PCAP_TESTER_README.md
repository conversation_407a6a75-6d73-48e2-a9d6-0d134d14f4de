# RADIUS PCAP测试工具使用说明

## 概述

这个工具用于从PCAP文件中读取RADIUS报文，并发送到您的RADIUS服务器进行测试和比对。支持四种不同的运行模式。

## 文件说明

- `pcap_radius_tester.py` - 主测试程序
- `pcap_parser.py` - PCAP文件解析模块
- `radius_comparator.py` - RADIUS响应比对模块
- `PCAP_TESTER_README.md` - 本说明文档

## 依赖安装

首先安装必要的依赖：

```bash
# 使用uv安装依赖
uv sync

# 或者手动安装
pip install scapy colorama
```

## 使用方法

### 基本语法

```bash
uv run python pcap_radius_tester.py [选项] <pcap文件1> [pcap文件2] ...
```

### 命令行参数

- `pcap_files` - PCAP文件路径（必需，可以指定多个文件）
- `--server` - RADIUS服务器IP地址（默认：127.0.0.1）
- `--auth-port` - 认证端口（默认：1812）
- `--acct-port` - 计费端口（默认：1813）
- `--secret` - RADIUS密钥（默认：huaweitest）
- `--mode` - 运行模式（1-4，默认：1）
- `--username` - 用户名（模式4必需）
- `--dictionary` - 字典文件路径（默认：conf/dictionary）

## 四种运行模式

### 模式1：发送所有报文，不做比对

```bash
uv run python pcap_radius_tester.py --mode 1 capture.pcap
```

- 读取PCAP文件中的所有RADIUS请求报文
- 依次发送到您的RADIUS服务器
- 不比对响应结果，只统计发送成功率
- 适用于基本的连通性测试

### 模式2：发送所有报文，比对结果

```bash
uv run python pcap_radius_tester.py --mode 2 capture.pcap
```

- 读取PCAP文件中的所有RADIUS请求和响应报文
- 依次发送请求到您的RADIUS服务器
- 将服务器响应与抓包文件中的原始响应进行比对
- 生成详细的比对报告
- 适用于全面的功能验证

### 模式3：确认后发送，比对结果

```bash
uv run python pcap_radius_tester.py --mode 3 capture.pcap
```

- 读取PCAP文件中的所有RADIUS报文
- 对每个请求报文，显示详细信息并询问是否发送
- 用户确认后发送到RADIUS服务器
- 比对响应结果并显示详细差异
- 适用于逐个验证特定报文

### 模式4：按用户名过滤发送

```bash
uv run python pcap_radius_tester.py --mode 4 --username "testuser" capture.pcap
```

- 只处理指定用户名的RADIUS报文
- 发送到RADIUS服务器并比对结果
- 显示该用户的所有测试结果
- 适用于针对特定用户的测试

## 使用示例

### 示例1：基本测试

```bash
# 测试本地RADIUS服务器
uv run python pcap_radius_tester.py --mode 1 radius_capture.pcap

# 测试远程RADIUS服务器
uv run python pcap_radius_tester.py --mode 1 --server ************0 --secret mysecret radius_capture.pcap
```

### 示例2：完整比对测试

```bash
# 比对所有报文
uv run python pcap_radius_tester.py --mode 2 auth.pcap acct.pcap

# 使用自定义配置
uv run python pcap_radius_tester.py \
    --mode 2 \
    --server 127.0.0.1 \
    --auth-port 1812 \
    --acct-port 1813 \
    --secret huaweitest \
    --dictionary conf/dictionary \
    capture.pcap
```

### 示例3：交互式测试

```bash
# 逐个确认发送
uv run python pcap_radius_tester.py --mode 3 radius_capture.pcap
```

### 示例4：用户特定测试

```bash
# 测试特定用户
uv run python pcap_radius_tester.py --mode 4 --username "18911111111" radius_capture.pcap

# 测试多个用户（需要多次运行）
uv run python pcap_radius_tester.py --mode 4 --username "user1" capture.pcap
uv run python pcap_radius_tester.py --mode 4 --username "user2" capture.pcap
```

## 输出说明

### 报文摘要

程序会首先显示PCAP文件的解析结果：

```
=== RADIUS报文摘要 ===
总报文数: 156
认证报文: 78
计费报文: 78
CoA报文: 0
请求报文: 78
响应报文: 78
唯一用户数: 5
用户列表:
  - user1: 32 个报文
  - user2: 24 个报文
  ...
```

### 比对结果

对于模式2、3、4，会显示详细的比对结果：

```
============================================================
请求信息:
  用户名: testuser
  源IP: ************
  目标IP: ***********
  报文类型: auth
  报文ID: 123

比对结果: ✗ 不匹配
响应代码: 原始=Access-Accept, 服务器=Access-Reject

发现 2 个差异:
  1. 响应代码不匹配: 原始=Access-Accept, 服务器=Access-Reject
  2. 缺失属性: Framed-IP-Address = **********
============================================================
```

### 汇总报告

测试完成后会生成汇总报告：

```
============================================================
比对汇总报告
============================================================
总测试数: 50
匹配数: 45 (90.0%)
不匹配数: 5 (10.0%)

不匹配详情:
  响应代码不匹配: 3
  属性不匹配: 4

前5个不匹配案例:
  1. 用户: user1, 差异数: 2
     主要差异: 响应代码不匹配: 原始=Access-Accept, 服务器=Access-Reject
  ...
============================================================
```

## 注意事项

1. **PCAP文件格式**：确保PCAP文件包含完整的UDP/RADIUS报文
2. **服务器配置**：确保您的RADIUS服务器正在运行并监听指定端口
3. **密钥配置**：确保使用正确的RADIUS密钥
4. **字典文件**：确保字典文件路径正确且包含所需的属性定义
5. **网络连通性**：确保测试机器能够访问RADIUS服务器
6. **权限问题**：某些系统可能需要管理员权限来读取PCAP文件

## 故障排除

### 常见问题

1. **无法解析PCAP文件**
   - 检查文件路径是否正确
   - 确认文件格式为标准PCAP格式
   - 检查文件权限

2. **连接RADIUS服务器失败**
   - 检查服务器IP和端口配置
   - 验证网络连通性
   - 确认RADIUS服务器正在运行

3. **认证失败**
   - 检查RADIUS密钥是否正确
   - 确认服务器配置允许测试客户端连接

4. **属性解析错误**
   - 检查字典文件是否完整
   - 确认字典文件包含所需的属性定义

### 调试建议

1. 先使用模式1进行基本连通性测试
2. 使用模式3逐个验证问题报文
3. 检查RADIUS服务器日志获取更多信息
4. 使用Wireshark等工具验证网络流量

## 扩展功能

如需添加更多功能，可以修改以下文件：

- `pcap_parser.py` - 添加更多报文解析功能
- `radius_comparator.py` - 自定义比对规则
- `pcap_radius_tester.py` - 添加新的测试模式

## 技术支持

如遇到问题，请检查：
1. Python版本（需要3.12+）
2. 依赖包版本
3. RADIUS服务器配置
4. 网络环境设置
