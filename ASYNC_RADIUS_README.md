# 异步协程版RADIUS服务器

## 概述

这是一个基于Python asyncio的高性能RADIUS服务器实现，支持高并发处理，每个RADIUS报文使用独立协程处理。

## 主要特性

### 🚀 高性能协程架构
- **单线程事件循环**：基于asyncio的高效事件驱动模型
- **协程并发**：每个RADIUS报文使用独立协程处理
- **可控并发**：通过信号量限制最大协程数量，防止资源耗尽
- **非阻塞I/O**：数据库操作完全异步，不阻塞其他请求

### 🛡️ 健壮性增强
- **安全属性访问**：所有RADIUS包属性访问都经过安全检查
- **异常处理**：完善的异常处理机制，单个请求失败不影响其他请求
- **超时控制**：每个协程都有超时保护
- **资源管理**：自动管理数据库连接池和协程生命周期

### 📊 性能监控
- **实时统计**：监控并发数、请求总数、错误率等
- **性能指标**：响应时间、吞吐量、资源使用情况
- **日志记录**：详细的性能和错误日志

## 架构对比

### 原版同步架构
```
收到报文 → 主线程处理 → 数据库查询(阻塞) → 回复 → 异步记录
```
- **优点**：简单直接
- **缺点**：数据库查询会阻塞主线程，影响并发性能

### 新版协程架构
```
收到报文 → 创建协程 → 异步数据库查询 → 回复 → 异步记录
           ↓
        协程池管理 → 信号量控制 → 超时保护
```
- **优点**：高并发、非阻塞、资源可控
- **缺点**：实现复杂度稍高

## 配置说明

### 性能配置 (conf/aaa_config.ini)

```ini
[performance]
# 最大并发协程数量
max_concurrent_tasks = 500

# 单个任务超时时间（秒）
task_timeout_seconds = 30.0

# 统计报告间隔（秒）
stats_interval = 60
```

### 数据库配置 (conf/db.ini)

```ini
[mysql]
user = aaa
password = smart@123.
host = **********
database = aaa
pool_size = 50
```

### 配置参数说明

- **max_concurrent_tasks**: 最大并发协程数，建议根据服务器性能调整
  - 低配置服务器：100-500
  - 中等配置服务器：500-1000
  - 高配置服务器：1000-5000

- **task_timeout_seconds**: 单个请求处理超时时间
  - 建议设置为 10-30 秒

- **pool_size**: 数据库连接池大小（在 conf/db.ini 中配置）
  - 建议设置为 max_concurrent_tasks 的 1/10 到 1/5
  - 与其他数据库模块共享此配置，保持一致性

## 使用方法

### 1. 安装依赖

```bash
pip install aiomysql asyncio
```

### 2. 启动异步服务器

```bash
# 使用启动脚本
python start_async_server.py

# 或直接运行
python -m aaaserver.async_server
```

### 3. 性能测试

```bash
# 基本性能测试
python test_async_performance.py --concurrent 100 --requests 10

# 压力测试
python test_async_performance.py --stress
```

## 性能优势

### 并发处理能力

| 架构类型 | 并发处理 | 内存使用 | CPU效率 | 响应时间 |
|---------|---------|---------|---------|---------|
| 同步版本 | 低 | 中等 | 低 | 高 |
| 协程版本 | 高 | 低 | 高 | 低 |

### 预期性能提升

- **并发处理能力**：提升 5-10 倍
- **响应时间**：降低 30-50%
- **内存使用**：降低 20-40%
- **CPU利用率**：提升 40-60%

## 监控和调优

### 1. 实时监控

服务器会每分钟输出统计信息：
```
Stats: Total=10000, Active=50, Max=120, Timeouts=5, Errors=2
```

### 2. 性能调优建议

#### 高并发场景
```ini
# conf/aaa_config.ini
max_concurrent_tasks = 2000
task_timeout_seconds = 15.0

# conf/db.ini
pool_size = 50
```

#### 低延迟场景
```ini
# conf/aaa_config.ini
max_concurrent_tasks = 500
task_timeout_seconds = 5.0

# conf/db.ini
pool_size = 20
```

#### 资源受限场景
```ini
# conf/aaa_config.ini
max_concurrent_tasks = 200
task_timeout_seconds = 30.0

# conf/db.ini
pool_size = 10
```

### 3. 监控指标

- **active_tasks**: 当前活跃协程数
- **max_concurrent**: 历史最大并发数
- **timeouts**: 超时请求数
- **errors**: 错误请求数
- **total_requests**: 总请求数

## 故障排除

### 常见问题

1. **协程数过多导致内存不足**
   - 降低 `max_concurrent_tasks`
   - 增加服务器内存

2. **数据库连接池耗尽**
   - 增加 `conf/db.ini` 中的 `pool_size`
   - 检查数据库连接泄漏

3. **请求超时频繁**
   - 增加 `task_timeout_seconds`
   - 优化数据库查询性能

### 日志分析

```bash
# 查看错误日志
grep "ERROR" logs/radius.log

# 查看性能统计
grep "Stats:" logs/radius.log

# 查看超时情况
grep "timeout" logs/radius.log
```

## 迁移指南

### 从同步版本迁移

1. **保持配置兼容**：现有配置文件无需修改
2. **数据库兼容**：数据库结构完全兼容
3. **功能兼容**：所有认证和计费功能保持一致
4. **逐步迁移**：可以并行运行测试

### 迁移步骤

1. 备份现有配置和数据
2. 安装异步版本依赖
3. 配置性能参数
4. 并行测试
5. 切换生产环境

## 开发和扩展

### 添加新功能

异步版本提供了更好的扩展性：

```python
async def custom_auth_handler(self, pkt):
    """自定义认证处理器"""
    # 异步处理逻辑
    result = await some_async_operation()
    return result
```

### 性能测试

```python
# 自定义性能测试
async def custom_performance_test():
    # 测试逻辑
    pass
```

## 总结

异步协程版RADIUS服务器通过以下技术实现了显著的性能提升：

1. **协程并发**：每个请求独立协程，避免阻塞
2. **资源控制**：信号量限制并发数，防止资源耗尽
3. **异步I/O**：数据库操作完全异步，提升吞吐量
4. **健壮性**：完善的错误处理和超时保护
5. **可监控**：实时性能统计和日志记录

这种架构特别适合高并发、低延迟的RADIUS认证场景，能够显著提升系统的整体性能和稳定性。
