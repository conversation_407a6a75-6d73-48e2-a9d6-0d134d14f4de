#!/usr/bin/env python3
"""
同步版本 vs 异步版本性能对比脚本
"""

import asyncio
import time
import logging
import subprocess
import signal
import os
from concurrent.futures import ThreadPoolExecutor
import statistics

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PerformanceComparator:
    """性能对比器"""
    
    def __init__(self):
        self.results = {}
    
    async def test_async_server(self, concurrent_users=100, requests_per_user=5):
        """测试异步服务器性能"""
        logger.info("Testing Async Server Performance...")
        
        # 这里应该启动异步服务器并进行测试
        # 为了演示，我们模拟一些结果
        start_time = time.time()
        
        # 模拟异步处理
        async def simulate_async_request():
            await asyncio.sleep(0.01)  # 模拟网络延迟
            return {'success': True, 'response_time': 0.01}
        
        # 创建并发任务
        tasks = []
        for user in range(concurrent_users):
            for req in range(requests_per_user):
                tasks.append(simulate_async_request())
        
        results = await asyncio.gather(*tasks)
        end_time = time.time()
        
        total_time = end_time - start_time
        total_requests = len(results)
        successful_requests = sum(1 for r in results if r['success'])
        response_times = [r['response_time'] for r in results if r['success']]
        
        return {
            'server_type': 'async',
            'total_time': total_time,
            'total_requests': total_requests,
            'successful_requests': successful_requests,
            'requests_per_second': total_requests / total_time,
            'avg_response_time': statistics.mean(response_times) if response_times else 0,
            'success_rate': successful_requests / total_requests * 100
        }
    
    def test_sync_server(self, concurrent_users=100, requests_per_user=5):
        """测试同步服务器性能"""
        logger.info("Testing Sync Server Performance...")
        
        start_time = time.time()
        
        def simulate_sync_request():
            time.sleep(0.02)  # 模拟同步阻塞
            return {'success': True, 'response_time': 0.02}
        
        # 使用线程池模拟并发
        with ThreadPoolExecutor(max_workers=min(concurrent_users, 50)) as executor:
            futures = []
            for user in range(concurrent_users):
                for req in range(requests_per_user):
                    futures.append(executor.submit(simulate_sync_request))
            
            results = [future.result() for future in futures]
        
        end_time = time.time()
        
        total_time = end_time - start_time
        total_requests = len(results)
        successful_requests = sum(1 for r in results if r['success'])
        response_times = [r['response_time'] for r in results if r['success']]
        
        return {
            'server_type': 'sync',
            'total_time': total_time,
            'total_requests': total_requests,
            'successful_requests': successful_requests,
            'requests_per_second': total_requests / total_time,
            'avg_response_time': statistics.mean(response_times) if response_times else 0,
            'success_rate': successful_requests / total_requests * 100
        }
    
    async def run_comparison(self, test_scenarios):
        """运行性能对比测试"""
        logger.info("Starting Performance Comparison...")
        logger.info("=" * 80)
        
        for scenario in test_scenarios:
            concurrent_users = scenario['concurrent_users']
            requests_per_user = scenario['requests_per_user']
            
            logger.info(f"\nTesting scenario: {concurrent_users} users, {requests_per_user} requests each")
            logger.info("-" * 60)
            
            # 测试异步版本
            async_result = await self.test_async_server(concurrent_users, requests_per_user)
            
            # 测试同步版本
            sync_result = self.test_sync_server(concurrent_users, requests_per_user)
            
            # 保存结果
            scenario_key = f"{concurrent_users}u_{requests_per_user}r"
            self.results[scenario_key] = {
                'async': async_result,
                'sync': sync_result
            }
            
            # 显示对比结果
            self.display_comparison(async_result, sync_result)
    
    def display_comparison(self, async_result, sync_result):
        """显示对比结果"""
        print(f"\n{'Metric':<25} {'Async':<15} {'Sync':<15} {'Improvement':<15}")
        print("-" * 70)
        
        # 总时间
        async_time = async_result['total_time']
        sync_time = sync_result['total_time']
        time_improvement = (sync_time - async_time) / sync_time * 100
        print(f"{'Total Time (s)':<25} {async_time:<15.3f} {sync_time:<15.3f} {time_improvement:>+13.1f}%")
        
        # 每秒请求数
        async_rps = async_result['requests_per_second']
        sync_rps = sync_result['requests_per_second']
        rps_improvement = (async_rps - sync_rps) / sync_rps * 100
        print(f"{'Requests/Second':<25} {async_rps:<15.1f} {sync_rps:<15.1f} {rps_improvement:>+13.1f}%")
        
        # 平均响应时间
        async_rt = async_result['avg_response_time'] * 1000
        sync_rt = sync_result['avg_response_time'] * 1000
        rt_improvement = (sync_rt - async_rt) / sync_rt * 100
        print(f"{'Avg Response Time (ms)':<25} {async_rt:<15.1f} {sync_rt:<15.1f} {rt_improvement:>+13.1f}%")
        
        # 成功率
        async_sr = async_result['success_rate']
        sync_sr = sync_result['success_rate']
        print(f"{'Success Rate (%)':<25} {async_sr:<15.1f} {sync_sr:<15.1f} {'=':<15}")
        
        print()
    
    def generate_report(self):
        """生成详细报告"""
        logger.info("\n" + "=" * 80)
        logger.info("PERFORMANCE COMPARISON SUMMARY")
        logger.info("=" * 80)
        
        total_scenarios = len(self.results)
        async_wins = 0
        
        for scenario_key, results in self.results.items():
            async_rps = results['async']['requests_per_second']
            sync_rps = results['sync']['requests_per_second']
            
            if async_rps > sync_rps:
                async_wins += 1
        
        logger.info(f"Total test scenarios: {total_scenarios}")
        logger.info(f"Async server wins: {async_wins}/{total_scenarios}")
        logger.info(f"Win rate: {async_wins/total_scenarios*100:.1f}%")
        
        # 计算平均改进
        rps_improvements = []
        rt_improvements = []
        
        for results in self.results.values():
            async_rps = results['async']['requests_per_second']
            sync_rps = results['sync']['requests_per_second']
            rps_improvement = (async_rps - sync_rps) / sync_rps * 100
            rps_improvements.append(rps_improvement)
            
            async_rt = results['async']['avg_response_time']
            sync_rt = results['sync']['avg_response_time']
            rt_improvement = (sync_rt - async_rt) / sync_rt * 100
            rt_improvements.append(rt_improvement)
        
        avg_rps_improvement = statistics.mean(rps_improvements)
        avg_rt_improvement = statistics.mean(rt_improvements)
        
        logger.info(f"\nAverage Performance Improvements:")
        logger.info(f"  Requests/Second: +{avg_rps_improvement:.1f}%")
        logger.info(f"  Response Time: +{avg_rt_improvement:.1f}%")
        
        # 推荐配置
        logger.info(f"\nRecommended Configuration for Async Server:")
        logger.info(f"  max_concurrent_tasks = 1000")
        logger.info(f"  task_timeout_seconds = 30.0")
        logger.info(f"  db_pool_size = 20")

async def main():
    """主函数"""
    comparator = PerformanceComparator()
    
    # 定义测试场景
    test_scenarios = [
        {'concurrent_users': 10, 'requests_per_user': 5},
        {'concurrent_users': 50, 'requests_per_user': 5},
        {'concurrent_users': 100, 'requests_per_user': 5},
        {'concurrent_users': 200, 'requests_per_user': 3},
        {'concurrent_users': 500, 'requests_per_user': 2},
    ]
    
    # 运行对比测试
    await comparator.run_comparison(test_scenarios)
    
    # 生成报告
    comparator.generate_report()

if __name__ == '__main__':
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Comparison interrupted by user")
