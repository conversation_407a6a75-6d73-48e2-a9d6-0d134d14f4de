#!/usr/bin/env python3
"""
启动异步RADIUS服务器的脚本
"""

import sys
import os
import asyncio
import signal
import logging

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from aaaserver.async_server import main as async_main
from aaaserver import async_dbutil

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def shutdown_handler(sig, loop):
    """优雅关闭处理器"""
    logger.info(f"Received signal {sig.name}, shutting down...")

    # 关闭数据库连接池
    await async_dbutil.close_db_pool()

    # 取消所有运行中的任务
    tasks = [task for task in asyncio.all_tasks() if task is not asyncio.current_task()]
    if tasks:
        logger.info(f"Cancelling {len(tasks)} outstanding tasks")
        for task in tasks:
            task.cancel()
        await asyncio.gather(*tasks, return_exceptions=True)

    # 停止事件循环
    loop.stop()

def main():
    """主函数"""
    logger.info("Starting Async RADIUS Server...")

    # 创建事件循环
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    # 设置信号处理器
    def make_signal_handler(signal_num, loop):
        def handler():
            asyncio.create_task(shutdown_handler(signal_num, loop))
        return handler

    for sig in (signal.SIGTERM, signal.SIGINT):
        loop.add_signal_handler(sig, make_signal_handler(sig, loop))

    try:
        # 运行异步服务器
        loop.run_until_complete(async_main())
    except KeyboardInterrupt:
        logger.info("Server interrupted by user")
    except Exception as e:
        logger.error(f"Server error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        try:
            # 清理资源
            loop.run_until_complete(async_dbutil.close_db_pool())
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
        finally:
            loop.close()
            logger.info("Server shutdown complete")

if __name__ == '__main__':
    main()
