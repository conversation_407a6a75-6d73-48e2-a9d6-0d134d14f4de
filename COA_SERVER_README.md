# CoA (Change of Authorization) 服务器

## 概述

本项目已成功集成了CoA (Change of Authorization) UDP服务器功能到异步RADIUS服务器中。CoA服务器允许RADIUS服务器主动向NAS设备发送授权变更或断开连接的请求。

## 功能特性

- **异步处理**: 基于asyncio的高性能异步处理
- **多协程支持**: 每个CoA请求使用独立协程处理
- **结构化日志**: 按照用户偏好的格式记录CoA操作日志
- **数据库集成**: 支持CoA操作记录到数据库
- **配置灵活**: 通过配置文件控制CoA服务器行为
- **错误处理**: 健壮的错误处理机制

## 配置

### 1. CoA服务器配置 (conf/aaa_config.ini)

```ini
[coa]
# CoA服务器启用开关
coa_enabled = 1

# CoA服务器监听端口
coa_port = 3799

# CoA请求处理超时时间（秒）
coa_timeout_seconds = 10.0

# CoA操作类型配置
supported_operations = disconnect,coa_request

# CoA响应配置
default_coa_response = ack
default_disconnect_response = ack
```

### 2. 日志配置

CoA操作日志会记录到 `logs/coa.log` 文件中，包含以下信息：
- 请求属性
- 处理过程
- 响应结果
- 错误信息

## 支持的CoA操作

### 1. CoA Request (Code: 43)
- 用于动态修改用户的授权属性
- 响应: CoA-ACK (44) 或 CoA-NAK (45)

### 2. Disconnect Request (Code: 40)  
- 用于强制断开用户连接
- 响应: Disconnect-ACK (41) 或 Disconnect-NAK (42)

## 启动服务器

### 方法1: 直接启动异步服务器
```bash
python -m aaaserver.async_server
```

### 方法2: 使用uv (推荐)
```bash
uv run -m aaaserver.async_server
```

### 方法3: 使用现有启动脚本
```bash
python start_async_server.py
```

## 测试CoA功能

### 使用现有的CoA客户端
```bash
python aaaserver/coa.py coa 127.0.0.1 testing123 test-session-123
python aaaserver/coa.py dis 127.0.0.1 testing123 test-session-456
```

## 日志示例

CoA操作的日志格式如下：

```
[2024-01-15 10:30:15.123] [INFO] [req-abc123][testuser][*************] Received CoA request from 192.168.1.1:3799
[2024-01-15 10:30:15.124] [INFO] [req-abc123][testuser][*************] **************************************** [Attributes] ****************************************
[2024-01-15 10:30:15.125] [INFO] [req-abc123][testuser][*************] User-Name: <EMAIL>
[2024-01-15 10:30:15.126] [INFO] [req-abc123][testuser][*************] Acct-Session-Id: session-123
[2024-01-15 10:30:15.127] [INFO] [req-abc123][testuser][*************] **************************************** [processing] ****************************************
[2024-01-15 10:30:15.128] [INFO] [req-abc123][testuser][*************] Processing CoA Request
[2024-01-15 10:30:15.129] [INFO] [req-abc123][testuser][*************] CoA Request accepted (CoA-ACK)
```

## 数据库支持

CoA操作会记录到数据库表 `coa_record` 中（如果表存在），包含以下字段：
- session_id: 会话ID
- user_name: 用户名
- user_domain: 用户域
- bras_ip: BRAS设备IP
- coa_type: CoA操作类型
- coa_result: 操作结果
- coa_time: 操作时间
- radius_server: RADIUS服务器标识
- client_ip: 客户端IP
- request_attributes: 请求属性

## 故障排除

### 1. CoA服务器无法启动
- 检查端口3799是否被占用
- 确认配置文件 `conf/aaa_config.ini` 中 `coa_enabled = 1`
- 检查日志文件 `logs/system.log` 中的错误信息

### 2. CoA请求无响应
- 确认客户端IP在BRAS配置中
- 检查防火墙设置
- 验证共享密钥配置

### 3. 日志记录问题
- 确认 `logs` 目录存在且可写
- 检查日志配置文件 `conf/logging.conf`

## 性能优化

- 默认最大并发协程数: 500
- 单个CoA请求超时: 10秒
- 可通过配置文件调整性能参数

## 安全注意事项

- 确保CoA端口(3799)仅对可信网络开放
- 使用强共享密钥
- 定期检查CoA操作日志
- 监控异常的CoA请求模式

## 服务端口

- **认证服务器**: 端口 1812
- **计费服务器**: 端口 1813
- **CoA服务器**: 端口 3799

## 架构说明

CoA UDP服务器已完全集成到异步RADIUS服务器架构中：
- 使用相同的协程池和并发控制机制
- 共享BRAS设备配置和密钥管理
- 统一的日志系统和错误处理
- 一致的数据库连接池管理
