# CoA (Change of Authorization) 踢下线功能使用说明

## 概述

异步RADIUS服务器现在支持完整的CoA踢下线功能，可以接收DM（Disconnect Message）请求并转发到相应的NAS设备，实现用户强制下线。

## 功能特性

### 🎯 **核心功能**
- **接收CoA请求**: 监听CoA端口接收外部CoA请求
- **会话查找**: 根据Session-ID或User-Name查找在线会话
- **NAS转发**: 将CoA请求转发到对应的NAS设备
- **响应处理**: 获取NAS响应并返回给客户端
- **数据库更新**: 成功断开连接后自动清理在线记录

### 📋 **支持的操作类型**
1. **Disconnect-Request**: 强制用户下线
2. **CoA-Request**: 变更用户授权属性

### 🔍 **查找方式**
- **按会话ID**: 使用`Acct-Session-Id`属性精确查找
- **按用户名**: 使用`User-Name`属性查找用户的所有在线会话

## 配置说明

### CoA服务器配置 (`conf/aaa_config.ini`)

```ini
[coa]
# CoA服务器启用开关
coa_enabled = 1

# CoA服务器监听端口
coa_port = 3799

# CoA请求处理超时时间（秒）
coa_timeout_seconds = 10.0

# 默认响应配置
default_coa_response = ack
default_disconnect_response = ack
```

## 使用方法

### 1. 启动异步RADIUS服务器

```bash
cd /data/project/smartaaa/radius
python -m aaaserver.async_server
```

服务器将在以下端口监听：
- **认证**: 1812 (UDP)
- **计费**: 1813 (UDP)  
- **CoA**: 3799 (UDP)

### 2. 发送CoA请求

#### 使用测试客户端

```bash
# 按会话ID踢下线
python test_coa_client.py --session-id "session123" --operation disconnect

# 按用户名踢下线
python test_coa_client.py --session-id "session123" --user-name "<EMAIL>" --operation disconnect

# 发送CoA变更请求
python test_coa_client.py --session-id "session123" --operation coa

# 指定服务器和端口
python test_coa_client.py --server ***********00 --port 3799 --session-id "session123"
```

#### 使用现有CoA模块

```python
from aaaserver.coa import process_coa_request

# 发送断开连接请求
result = process_coa_request(
    operation="dis",
    address="127.0.0.1:3799", 
    secret=b"testing123",
    sessionid="session123"
)
```

## 工作流程

### Disconnect-Request 处理流程

1. **接收请求**: CoA服务器接收Disconnect-Request
2. **解析属性**: 提取Session-ID和User-Name
3. **查找会话**: 
   - 如果有Session-ID，查找对应的在线记录
   - 如果只有User-Name，查找用户的所有在线会话
4. **获取NAS信息**: 从在线记录中获取NAS IP地址
5. **转发请求**: 将Disconnect-Request转发到对应的NAS
6. **处理响应**: 接收NAS的响应
7. **更新数据库**: 如果NAS确认断开成功，删除在线记录
8. **返回响应**: 将NAS的响应返回给客户端

### CoA-Request 处理流程

1. **接收请求**: CoA服务器接收CoA-Request
2. **查找会话**: 根据Session-ID查找在线记录
3. **转发请求**: 将CoA-Request转发到对应的NAS
4. **返回响应**: 将NAS的响应返回给客户端

## 响应代码

### Disconnect响应
- **40 (Disconnect-ACK)**: 断开连接成功
- **41 (Disconnect-NAK)**: 断开连接失败

### CoA响应  
- **44 (CoA-ACK)**: CoA请求成功
- **45 (CoA-NAK)**: CoA请求失败

## 日志记录

CoA操作会记录到 `logs/coa.log` 文件中，包含：

```
[2025-05-31 16:30:15.123] [INFO] [req-id][user][client-ip] Received CoA request from ***********00:12345
[2025-05-31 16:30:15.124] [INFO] [req-id][user][client-ip] Processing Disconnect Request
[2025-05-31 16:30:15.125] [INFO] [req-id][user][client-ip] Found online session for session123, NAS IP: ***********
[2025-05-31 16:30:15.200] [INFO] [req-id][user][client-ip] Disconnect forwarded to NAS ***********, response code: 40
[2025-05-31 16:30:15.201] [INFO] [req-id][user][client-ip] Successfully removed online session session123
```

## 故障排除

### 常见问题

1. **会话未找到**
   - 检查Session-ID是否正确
   - 确认用户确实在线
   - 查看在线记录表

2. **NAS转发失败**
   - 检查NAS IP地址配置
   - 验证NAS共享密钥
   - 确认NAS设备支持CoA

3. **超时错误**
   - 调整`coa_timeout_seconds`配置
   - 检查网络连接
   - 验证NAS设备状态

### 调试方法

1. **查看日志**: 检查 `logs/coa.log` 和 `logs/system.log`
2. **测试连通性**: 使用ping测试到NAS的网络连接
3. **验证配置**: 检查NAS设备的CoA配置

## 安全考虑

1. **共享密钥**: 确保使用强密钥并定期更换
2. **网络隔离**: CoA流量应在管理网络中传输
3. **访问控制**: 限制可以发送CoA请求的客户端IP
4. **日志监控**: 监控CoA操作日志，及时发现异常

## 性能优化

1. **并发处理**: 使用异步协程处理多个CoA请求
2. **连接池**: 复用到NAS的连接
3. **超时设置**: 合理设置超时时间避免阻塞
4. **批量操作**: 对同一用户的多个会话进行批量处理
