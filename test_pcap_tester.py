#!/usr/bin/env python3
"""
RADIUS PCAP测试工具的测试脚本
用于验证工具的基本功能
"""

import os
import sys
import tempfile
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, '.')

from pcap_parser import RadiusPcapParser
from radius_comparator import RadiusComparator
from pcap_radius_tester import RadiusPcapTester


def test_parser():
    """测试PCAP解析器"""
    print("=== 测试PCAP解析器 ===")
    
    try:
        parser = RadiusPcapParser("conf/dictionary")
        print("✓ PCAP解析器初始化成功")
        
        # 测试解析不存在的文件
        packets = parser.parse_pcap_file("nonexistent.pcap")
        print(f"✓ 处理不存在文件: 返回 {len(packets)} 个报文")
        
        return True
    except Exception as e:
        print(f"✗ PCAP解析器测试失败: {e}")
        return False


def test_comparator():
    """测试响应比对器"""
    print("\n=== 测试响应比对器 ===")
    
    try:
        comparator = RadiusComparator()
        print("✓ 响应比对器初始化成功")
        
        # 测试响应代码映射
        test_codes = [1, 2, 3, 4, 5, 40, 41, 42, 43, 44, 45]
        for code in test_codes:
            name = comparator.response_codes.get(code, f"Unknown({code})")
            print(f"  代码 {code}: {name}")
        
        print("✓ 响应代码映射测试通过")
        return True
    except Exception as e:
        print(f"✗ 响应比对器测试失败: {e}")
        return False


def test_tester_init():
    """测试主测试器初始化"""
    print("\n=== 测试主测试器初始化 ===")
    
    try:
        # 测试默认配置
        tester = RadiusPcapTester()
        print("✓ 使用默认配置初始化成功")
        
        # 测试自定义配置
        tester2 = RadiusPcapTester(
            server_ip="***********",
            auth_port=1812,
            acct_port=1813,
            secret=b"testsecret"
        )
        print("✓ 使用自定义配置初始化成功")
        
        return True
    except Exception as e:
        print(f"✗ 主测试器初始化失败: {e}")
        return False


def test_dictionary_loading():
    """测试字典文件加载"""
    print("\n=== 测试字典文件加载 ===")
    
    try:
        from pyrad.dictionary import Dictionary
        
        # 测试主字典文件
        dict_path = "conf/dictionary"
        if Path(dict_path).exists():
            dictionary = Dictionary(dict_path)
            print(f"✓ 主字典文件加载成功: {dict_path}")
            
            # 显示一些基本属性
            basic_attrs = ["User-Name", "User-Password", "NAS-IP-Address", "Service-Type"]
            for attr in basic_attrs:
                if attr in dictionary.attributes:
                    attr_info = dictionary.attributes[attr]
                    print(f"  {attr}: 类型={attr_info.type}, 代码={attr_info.code}")
                else:
                    print(f"  {attr}: 未找到")
        else:
            print(f"✗ 字典文件不存在: {dict_path}")
            return False
        
        return True
    except Exception as e:
        print(f"✗ 字典文件加载失败: {e}")
        return False


def test_client_creation():
    """测试RADIUS客户端创建"""
    print("\n=== 测试RADIUS客户端创建 ===")
    
    try:
        from pyrad.client import Client
        from pyrad.dictionary import Dictionary
        
        dictionary = Dictionary("conf/dictionary")
        
        # 创建认证客户端
        auth_client = Client(
            server="127.0.0.1",
            secret=b"testsecret",
            dict=dictionary,
            authport=1812
        )
        print("✓ 认证客户端创建成功")
        
        # 创建计费客户端
        acct_client = Client(
            server="127.0.0.1", 
            secret=b"testsecret",
            dict=dictionary,
            acctport=1813
        )
        print("✓ 计费客户端创建成功")
        
        return True
    except Exception as e:
        print(f"✗ RADIUS客户端创建失败: {e}")
        return False


def test_packet_creation():
    """测试RADIUS报文创建"""
    print("\n=== 测试RADIUS报文创建 ===")
    
    try:
        from pyrad.client import Client
        from pyrad.dictionary import Dictionary
        import pyrad.packet
        
        dictionary = Dictionary("conf/dictionary")
        client = Client(
            server="127.0.0.1",
            secret=b"testsecret", 
            dict=dictionary
        )
        
        # 创建认证请求
        auth_req = client.CreateAuthPacket(
            code=pyrad.packet.AccessRequest,
            User_Name="testuser"
        )
        auth_req["User-Password"] = auth_req.PwCrypt("testpass")
        auth_req["NAS-IP-Address"] = "***********0"
        print("✓ 认证请求报文创建成功")
        
        # 创建计费请求
        acct_req = client.CreateAcctPacket(User_Name="testuser")
        acct_req["Acct-Status-Type"] = "Start"
        acct_req["NAS-IP-Address"] = "***********0"
        print("✓ 计费请求报文创建成功")
        
        return True
    except Exception as e:
        print(f"✗ RADIUS报文创建失败: {e}")
        return False


def check_dependencies():
    """检查依赖包"""
    print("\n=== 检查依赖包 ===")
    
    required_packages = [
        "scapy",
        "colorama", 
        "pyrad",
        "six",
        "netaddr"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺失的依赖包: {', '.join(missing_packages)}")
        print("请运行: uv sync 或 pip install " + " ".join(missing_packages))
        return False
    
    return True


def check_files():
    """检查必要文件"""
    print("\n=== 检查必要文件 ===")
    
    required_files = [
        "pcap_parser.py",
        "radius_comparator.py", 
        "pcap_radius_tester.py",
        "conf/dictionary"
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path} (缺失)")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n缺失的文件: {', '.join(missing_files)}")
        return False
    
    return True


def main():
    """主测试函数"""
    print("RADIUS PCAP测试工具 - 功能验证")
    print("=" * 50)
    
    tests = [
        ("检查必要文件", check_files),
        ("检查依赖包", check_dependencies),
        ("字典文件加载", test_dictionary_loading),
        ("RADIUS客户端创建", test_client_creation),
        ("RADIUS报文创建", test_packet_creation),
        ("PCAP解析器", test_parser),
        ("响应比对器", test_comparator),
        ("主测试器初始化", test_tester_init),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"测试失败: {test_name}")
        except Exception as e:
            print(f"测试异常: {test_name} - {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过，工具可以正常使用")
        print("\n使用示例:")
        print("uv run python pcap_radius_tester.py --mode 1 your_capture.pcap")
    else:
        print("✗ 部分测试失败，请检查配置和依赖")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
