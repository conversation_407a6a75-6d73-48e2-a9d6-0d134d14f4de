#!/usr/bin/env python3
"""
PCAP文件解析模块
用于从PCAP文件中提取RADIUS报文
"""

import struct
import socket
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from scapy.all import rdpcap, UDP, IP
from scapy.packet import Packet as ScapyPacket
import pyrad.packet
from pyrad.dictionary import Dictionary


@dataclass
class RadiusPacketInfo:
    """RADIUS报文信息"""
    timestamp: float
    src_ip: str
    dst_ip: str
    src_port: int
    dst_port: int
    packet_type: str  # 'auth' or 'acct'
    raw_data: bytes
    parsed_packet: Optional[pyrad.packet.Packet] = None
    username: Optional[str] = None
    packet_code: Optional[int] = None
    packet_id: Optional[int] = None
    is_request: bool = True
    is_response: bool = False


class RadiusPcapParser:
    """RADIUS PCAP文件解析器"""
    
    def __init__(self, dictionary_path: str = "conf/dictionary"):
        """
        初始化解析器
        
        Args:
            dictionary_path: RADIUS字典文件路径
        """
        self.dictionary = Dictionary(dictionary_path)
        self.packets: List[RadiusPacketInfo] = []
        
    def parse_pcap_file(self, pcap_file: str, max_packets: int = None) -> List[RadiusPacketInfo]:
        """
        解析PCAP文件

        Args:
            pcap_file: PCAP文件路径
            max_packets: 最大解析报文数量（None表示解析全部）

        Returns:
            RADIUS报文信息列表
        """
        print(f"正在解析PCAP文件: {pcap_file}")
        if max_packets:
            print(f"限制解析数量: {max_packets}")

        try:
            # 读取PCAP文件
            print("正在读取PCAP文件...")
            packets = rdpcap(pcap_file)
            print(f"PCAP文件包含 {len(packets)} 个网络报文")

            radius_packets = []
            processed_count = 0

            for i, pkt in enumerate(packets):
                # 显示进度
                if i % 10000 == 0 and i > 0:
                    print(f"已处理 {i}/{len(packets)} 个网络报文，找到 {len(radius_packets)} 个RADIUS报文")

                # 检查是否达到最大数量限制
                if max_packets and len(radius_packets) >= max_packets:
                    print(f"已达到最大解析数量限制: {max_packets}")
                    break

                # 检查是否为UDP包
                if not pkt.haslayer(UDP):
                    continue

                udp_layer = pkt[UDP]
                ip_layer = pkt[IP]

                # 检查是否为RADIUS端口 (1812认证, 1813计费, 3799 CoA)
                if udp_layer.dport not in [1812, 1813, 3799] and udp_layer.sport not in [1812, 1813, 3799]:
                    continue

                # 提取UDP载荷
                radius_data = bytes(udp_layer.payload)
                if len(radius_data) < 20:  # RADIUS最小包长度
                    continue

                # 确定包类型
                packet_type = self._determine_packet_type(udp_layer.dport, udp_layer.sport)
                if not packet_type:
                    continue

                # 创建RADIUS报文信息
                radius_info = RadiusPacketInfo(
                    timestamp=float(pkt.time),
                    src_ip=ip_layer.src,
                    dst_ip=ip_layer.dst,
                    src_port=udp_layer.sport,
                    dst_port=udp_layer.dport,
                    packet_type=packet_type,
                    raw_data=radius_data
                )

                # 解析RADIUS报文
                self._parse_radius_packet(radius_info)
                radius_packets.append(radius_info)
                processed_count += 1

            print(f"解析完成，共找到 {len(radius_packets)} 个RADIUS报文")
            self.packets = radius_packets
            return radius_packets

        except Exception as e:
            print(f"解析PCAP文件时出错: {e}")
            return []
    
    def _determine_packet_type(self, dport: int, sport: int) -> Optional[str]:
        """
        确定RADIUS报文类型
        
        Args:
            dport: 目标端口
            sport: 源端口
            
        Returns:
            报文类型 ('auth', 'acct', 'coa') 或 None
        """
        if dport == 1812 or sport == 1812:
            return 'auth'
        elif dport == 1813 or sport == 1813:
            return 'acct'
        elif dport == 3799 or sport == 3799:
            return 'coa'
        return None
    
    def _parse_radius_packet(self, radius_info: RadiusPacketInfo):
        """
        解析RADIUS报文内容
        
        Args:
            radius_info: RADIUS报文信息对象
        """
        try:
            # 解析RADIUS报文头部
            if len(radius_info.raw_data) < 4:
                return
                
            code, packet_id, length = struct.unpack('!BBH', radius_info.raw_data[:4])
            radius_info.packet_code = code
            radius_info.packet_id = packet_id
            
            # 判断是请求还是响应
            radius_info.is_request = self._is_request_packet(code)
            radius_info.is_response = not radius_info.is_request
            
            # 尝试解析完整的RADIUS报文
            if radius_info.packet_type == 'auth':
                parsed_packet = pyrad.packet.AuthPacket(
                    packet=radius_info.raw_data,
                    dict=self.dictionary
                )
            elif radius_info.packet_type == 'acct':
                parsed_packet = pyrad.packet.AcctPacket(
                    packet=radius_info.raw_data,
                    dict=self.dictionary
                )
            elif radius_info.packet_type == 'coa':
                parsed_packet = pyrad.packet.CoAPacket(
                    packet=radius_info.raw_data,
                    dict=self.dictionary
                )
            else:
                return
                
            radius_info.parsed_packet = parsed_packet
            
            # 提取用户名
            if 'User-Name' in parsed_packet:
                username_value = parsed_packet['User-Name'][0]
                if isinstance(username_value, bytes):
                    radius_info.username = username_value.decode('utf-8', errors='ignore')
                else:
                    radius_info.username = str(username_value)
                
        except Exception as e:
            print(f"解析RADIUS报文时出错: {e}")
    
    def _is_request_packet(self, code: int) -> bool:
        """
        判断是否为请求报文
        
        Args:
            code: RADIUS报文代码
            
        Returns:
            是否为请求报文
        """
        # RADIUS请求报文代码
        request_codes = {
            1,   # Access-Request
            4,   # Accounting-Request
            40,  # Disconnect-Request
            43,  # CoA-Request
        }
        return code in request_codes
    
    def get_packets_by_username(self, username: str) -> List[RadiusPacketInfo]:
        """
        根据用户名过滤报文
        
        Args:
            username: 用户名
            
        Returns:
            匹配的RADIUS报文列表
        """
        return [pkt for pkt in self.packets if pkt.username == username]
    
    def get_request_packets(self) -> List[RadiusPacketInfo]:
        """
        获取所有请求报文
        
        Returns:
            请求报文列表
        """
        return [pkt for pkt in self.packets if pkt.is_request]
    
    def get_response_packets(self) -> List[RadiusPacketInfo]:
        """
        获取所有响应报文
        
        Returns:
            响应报文列表
        """
        return [pkt for pkt in self.packets if pkt.is_response]
    
    def print_packet_summary(self):
        """打印报文摘要信息"""
        if not self.packets:
            print("没有找到RADIUS报文")
            return
            
        print(f"\n=== RADIUS报文摘要 ===")
        print(f"总报文数: {len(self.packets)}")
        
        # 按类型统计
        auth_count = len([p for p in self.packets if p.packet_type == 'auth'])
        acct_count = len([p for p in self.packets if p.packet_type == 'acct'])
        coa_count = len([p for p in self.packets if p.packet_type == 'coa'])
        
        print(f"认证报文: {auth_count}")
        print(f"计费报文: {acct_count}")
        print(f"CoA报文: {coa_count}")
        
        # 按请求/响应统计
        request_count = len([p for p in self.packets if p.is_request])
        response_count = len([p for p in self.packets if p.is_response])
        
        print(f"请求报文: {request_count}")
        print(f"响应报文: {response_count}")
        
        # 用户名统计
        usernames = set(p.username for p in self.packets if p.username)
        print(f"唯一用户数: {len(usernames)}")
        
        if usernames:
            print("用户列表:")
            for username in sorted(usernames):
                user_packets = self.get_packets_by_username(username)
                print(f"  - {username}: {len(user_packets)} 个报文")


if __name__ == "__main__":
    # 测试代码
    parser = RadiusPcapParser()
    
    # 示例用法
    print("RADIUS PCAP解析器测试")
    print("请将PCAP文件放在当前目录下，然后修改文件名进行测试")
