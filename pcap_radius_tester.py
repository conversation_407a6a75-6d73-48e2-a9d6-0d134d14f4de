#!/usr/bin/env python3
"""
RADIUS PCAP测试工具
用于从PCAP文件读取RADIUS报文并发送到RADIUS服务器进行测试
"""

import sys
import time
import socket
import argparse
from typing import List, Optional, Tuple
from pathlib import Path

# 导入项目模块
from pcap_parser import RadiusPcapParser, RadiusPacketInfo
from radius_comparator import RadiusComparator, ComparisonResult
from pyrad.client import Client
from pyrad.dictionary import Dictionary
import pyrad.packet


class RadiusPcapTester:
    """RADIUS PCAP测试器"""
    
    def __init__(self,
                 server_ip: str = "127.0.0.1",
                 auth_port: int = 1812,
                 acct_port: int = 1813,
                 secret: bytes = b"huaweitest",
                 dictionary_path: str = "conf/dictionary",
                 ignore_extra_attributes: bool = False,
                 debug_matching: bool = False):
        """
        初始化测试器

        Args:
            server_ip: RADIUS服务器IP
            auth_port: 认证端口
            acct_port: 计费端口
            secret: RADIUS密钥
            dictionary_path: 字典文件路径
            ignore_extra_attributes: 是否忽略服务器响应中的额外属性
            debug_matching: 是否显示匹配调试信息
        """
        self.server_ip = server_ip
        self.auth_port = auth_port
        self.acct_port = acct_port
        self.secret = secret
        self.dictionary_path = dictionary_path
        self.debug_matching = debug_matching

        # 初始化组件
        self.parser = RadiusPcapParser(dictionary_path)
        self.comparator = RadiusComparator(ignore_extra_attributes)

        # 初始化RADIUS客户端
        self.auth_client = None
        self.acct_client = None
        self._init_clients()
    
    def _init_clients(self):
        """初始化RADIUS客户端"""
        try:
            dictionary = Dictionary(self.dictionary_path)
            
            # 认证客户端
            self.auth_client = Client(
                server=self.server_ip,
                secret=self.secret,
                dict=dictionary,
                authport=self.auth_port
            )
            
            # 计费客户端  
            self.acct_client = Client(
                server=self.server_ip,
                secret=self.secret,
                dict=dictionary,
                acctport=self.acct_port
            )
            
            print(f"RADIUS客户端初始化成功")
            print(f"服务器: {self.server_ip}")
            print(f"认证端口: {self.auth_port}")
            print(f"计费端口: {self.acct_port}")
            
        except Exception as e:
            print(f"初始化RADIUS客户端失败: {e}")
            sys.exit(1)
    
    def load_pcap_files(self, pcap_files: List[str], max_packets: int = None) -> List[RadiusPacketInfo]:
        """
        加载PCAP文件

        Args:
            pcap_files: PCAP文件路径列表
            max_packets: 最大解析报文数量

        Returns:
            所有RADIUS报文列表
        """
        all_packets = []

        for pcap_file in pcap_files:
            if not Path(pcap_file).exists():
                print(f"警告: PCAP文件不存在: {pcap_file}")
                continue

            print(f"\n正在加载PCAP文件: {pcap_file}")

            # 计算当前文件的最大解析数量
            remaining_quota = None
            if max_packets:
                remaining_quota = max_packets - len(all_packets)
                if remaining_quota <= 0:
                    print(f"已达到最大解析数量限制: {max_packets}")
                    break

            packets = self.parser.parse_pcap_file(pcap_file, remaining_quota)
            all_packets.extend(packets)

            if max_packets and len(all_packets) >= max_packets:
                print(f"已达到最大解析数量限制: {max_packets}")
                break

        # 按时间戳排序
        print("正在按时间戳排序...")
        all_packets.sort(key=lambda x: x.timestamp)

        print(f"\n总共加载了 {len(all_packets)} 个RADIUS报文")
        return all_packets
    
    def send_radius_packet(self, packet_info: RadiusPacketInfo) -> Optional[pyrad.packet.Packet]:
        """
        发送RADIUS报文到服务器
        
        Args:
            packet_info: RADIUS报文信息
            
        Returns:
            服务器响应报文
        """
        try:
            # 确保报文属性已解析
            if not packet_info.parsed_packet:
                self.parser.parse_packet_attributes(packet_info)

            if not packet_info.parsed_packet:
                print(f"跳过无法解析的报文")
                return None
            
            # 选择合适的客户端
            if packet_info.packet_type == 'auth':
                client = self.auth_client
            elif packet_info.packet_type == 'acct':
                client = self.acct_client
            else:
                print(f"不支持的报文类型: {packet_info.packet_type}")
                return None
            
            # 创建新的请求报文
            if packet_info.packet_type == 'auth':
                request = client.CreateAuthPacket(code=packet_info.parsed_packet.code)
            else:
                request = client.CreateAcctPacket(code=packet_info.parsed_packet.code)
            
            # 复制属性
            for attr_name in packet_info.parsed_packet.keys():
                try:
                    request[attr_name] = packet_info.parsed_packet[attr_name]
                except Exception as e:
                    print(f"复制属性 {attr_name} 时出错: {e}")
            
            # 发送报文
            print(f"发送 {packet_info.packet_type} 报文到 {self.server_ip}")
            response = client.SendPacket(request)
            
            return response
            
        except Exception as e:
            print(f"发送RADIUS报文时出错: {e}")
            return None
    
    def mode1_send_all_no_compare(self, packets: List[RadiusPacketInfo]):
        """
        模式1: 发送所有报文，不做比对
        
        Args:
            packets: RADIUS报文列表
        """
        print(f"\n=== 模式1: 发送所有报文，不做比对 ===")
        
        request_packets = [p for p in packets if p.is_request]
        print(f"将发送 {len(request_packets)} 个请求报文")
        
        success_count = 0
        for i, packet_info in enumerate(request_packets, 1):
            print(f"\n[{i}/{len(request_packets)}] 处理报文:")
            print(f"  用户名: {packet_info.username or 'N/A'}")
            print(f"  类型: {packet_info.packet_type}")
            
            response = self.send_radius_packet(packet_info)
            if response:
                success_count += 1
                print(f"  ✓ 发送成功，收到响应")
            else:
                print(f"  ✗ 发送失败")
        
        print(f"\n=== 模式1完成 ===")
        print(f"成功发送: {success_count}/{len(request_packets)}")
    
    def mode2_send_all_with_compare(self, packets: List[RadiusPacketInfo]):
        """
        模式2: 发送所有报文，比对结果
        
        Args:
            packets: RADIUS报文列表
        """
        print(f"\n=== 模式2: 发送所有报文，比对结果 ===")
        
        # 分离请求和响应
        request_packets = [p for p in packets if p.is_request]
        response_packets = [p for p in packets if p.is_response]
        
        print(f"请求报文: {len(request_packets)}")
        print(f"响应报文: {len(response_packets)}")
        
        # 建立请求-响应映射
        response_map = self._build_response_map(request_packets, response_packets)
        
        results = []
        for i, packet_info in enumerate(request_packets, 1):
            print(f"\n[{i}/{len(request_packets)}] 处理报文:")
            print(f"  用户名: {packet_info.username or 'N/A'}")
            print(f"  类型: {packet_info.packet_type}")
            
            # 发送请求
            server_response = self.send_radius_packet(packet_info)
            if not server_response:
                print(f"  ✗ 发送失败")
                continue
            
            # 查找对应的原始响应
            original_response = response_map.get(packet_info.packet_id)
            if not original_response:
                print(f"  ⚠ 未找到对应的原始响应")
                continue
            
            # 比对结果
            comparison = self.comparator.compare_responses(original_response, server_response)
            results.append((packet_info, comparison))
            
            if comparison.is_match:
                print(f"  ✓ 响应匹配")
            else:
                print(f"  ✗ 响应不匹配 ({len(comparison.differences)} 个差异)")
        
        # 生成汇总报告
        self.comparator.generate_summary_report(results)
    
    def mode3_send_with_confirmation(self, packets: List[RadiusPacketInfo]):
        """
        模式3: 确认后发送报文，比对结果

        Args:
            packets: RADIUS报文列表
        """
        print(f"\n=== 模式3: 确认后发送报文，比对结果 ===")

        # 分离请求和响应
        request_packets = [p for p in packets if p.is_request]
        response_packets = [p for p in packets if p.is_response]

        print(f"请求报文: {len(request_packets)}")
        print(f"响应报文: {len(response_packets)}")

        # 建立请求-响应映射
        response_map = self._build_response_map(request_packets, response_packets)

        results = []
        for i, packet_info in enumerate(request_packets, 1):
            print(f"\n[{i}/{len(request_packets)}] 报文信息:")
            print(f"  用户名: {packet_info.username or 'N/A'}")
            print(f"  类型: {packet_info.packet_type}")
            print(f"  源IP: {packet_info.src_ip}")
            print(f"  目标IP: {packet_info.dst_ip}")
            print(f"  报文ID: {packet_info.packet_id}")

            # 显示时间戳
            import time
            timestamp_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(packet_info.timestamp))
            print(f"  时间戳: {timestamp_str}")

            # 显示报文属性（需要时才解析）
            print(f"  属性:")
            try:
                # 确保报文属性已解析
                if packet_info.parsed_packet is None:
                    self.parser.parse_packet_attributes(packet_info)

                if packet_info.parsed_packet:
                    # 获取属性名列表并转换为字符串进行排序
                    attr_names = list(packet_info.parsed_packet.keys())
                    attr_names_str = [str(name) for name in attr_names]
                    attr_names_str.sort()

                    for attr_name_str in attr_names_str:
                        # 找到对应的原始属性名
                        original_attr_name = None
                        for name in attr_names:
                            if str(name) == attr_name_str:
                                original_attr_name = name
                                break

                        if original_attr_name is not None:
                            attr_values = packet_info.parsed_packet[original_attr_name]
                            if len(attr_values) == 1:
                                print(f"    {attr_name_str}: {attr_values[0]}")
                            else:
                                print(f"    {attr_name_str}: {attr_values}")
                else:
                    print(f"    无法解析报文属性")
            except Exception as e:
                print(f"    显示属性时出错: {e}")
                # 备用方案：显示基本信息
                print(f"    报文代码: {packet_info.packet_code}")
                print(f"    报文长度: {len(packet_info.raw_data)} 字节")

            # 询问是否发送
            while True:
                choice = input(f"\n是否发送此报文? (y/n/q): ").lower().strip()
                if choice in ['y', 'yes']:
                    break
                elif choice in ['n', 'no']:
                    print("跳过此报文")
                    continue
                elif choice in ['q', 'quit']:
                    print("退出测试")
                    return
                else:
                    print("请输入 y/n/q")

            # 发送请求
            server_response = self.send_radius_packet(packet_info)
            if not server_response:
                print(f"  ✗ 发送失败")
                continue

            # 查找对应的原始响应
            original_response = response_map.get(packet_info.packet_id)
            if not original_response:
                print(f"  ⚠ 未找到对应的原始响应")
                continue

            # 比对结果
            comparison = self.comparator.compare_responses(original_response, server_response)
            results.append((packet_info, comparison))

            # 显示详细比对结果
            self.comparator.print_comparison_result(comparison, packet_info)

        # 生成汇总报告
        if results:
            self.comparator.generate_summary_report(results)

    def mode4_send_by_username(self, packets: List[RadiusPacketInfo], username: str):
        """
        模式4: 按用户名过滤发送报文，比对结果

        Args:
            packets: RADIUS报文列表
            username: 目标用户名
        """
        print(f"\n=== 模式4: 按用户名过滤发送报文 ===")
        print(f"目标用户名: {username}")

        # 过滤指定用户的报文
        user_packets = [p for p in packets if p.username == username]
        if not user_packets:
            print(f"未找到用户 '{username}' 的报文")
            return

        print(f"找到 {len(user_packets)} 个用户 '{username}' 的报文")

        # 分离请求和响应
        request_packets = [p for p in user_packets if p.is_request]
        response_packets = [p for p in user_packets if p.is_response]

        print(f"请求报文: {len(request_packets)}")
        print(f"响应报文: {len(response_packets)}")

        if not request_packets:
            print("没有找到请求报文")
            return

        # 建立请求-响应映射
        response_map = self._build_response_map(request_packets, response_packets)

        results = []
        for i, packet_info in enumerate(request_packets, 1):
            print(f"\n[{i}/{len(request_packets)}] 处理报文:")
            print(f"  类型: {packet_info.packet_type}")
            import time
            print(f"  时间戳: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(packet_info.timestamp))}")

            # 发送请求
            server_response = self.send_radius_packet(packet_info)
            if not server_response:
                print(f"  ✗ 发送失败")
                continue

            # 查找对应的原始响应
            original_response = response_map.get(packet_info.packet_id)
            if not original_response:
                print(f"  ⚠ 未找到对应的原始响应")
                continue

            # 比对结果
            comparison = self.comparator.compare_responses(original_response, server_response)
            results.append((packet_info, comparison))

            if comparison.is_match:
                print(f"  ✓ 响应匹配")
            else:
                print(f"  ✗ 响应不匹配 ({len(comparison.differences)} 个差异)")
                # 显示详细差异
                for diff in comparison.differences:
                    print(f"    - {diff}")

        # 生成汇总报告
        if results:
            self.comparator.generate_summary_report(results)

    def _build_response_map(self,
                           request_packets: List[RadiusPacketInfo],
                           response_packets: List[RadiusPacketInfo]) -> dict:
        """
        建立请求-响应映射

        Args:
            request_packets: 请求报文列表
            response_packets: 响应报文列表

        Returns:
            请求报文到响应报文的映射
        """
        response_map = {}

        # 为每个请求报文找到最匹配的响应报文
        for request in request_packets:
            best_response = None
            best_score = -1

            for response in response_packets:
                # 计算匹配分数
                score = self._calculate_match_score(request, response)
                if score > best_score:
                    best_score = score
                    best_response = response

            # 只有当匹配分数足够高时才建立映射
            if best_score > 0:
                response_map[request.packet_id] = best_response
                if self.debug_matching:
                    print(f"  ✓ 匹配请求ID {request.packet_id} -> 响应ID {best_response.packet_id} (分数: {best_score})")
            else:
                if self.debug_matching:
                    print(f"  ✗ 警告: 请求ID {request.packet_id} 未找到匹配的响应")

        return response_map

    def _calculate_match_score(self, request: RadiusPacketInfo, response: RadiusPacketInfo) -> int:
        """
        计算请求和响应的匹配分数
        使用严格的匹配条件：Packet ID + NAS-IP + 时间差小于2秒

        Args:
            request: 请求报文
            response: 响应报文

        Returns:
            匹配分数（越高越匹配，-1表示不匹配）
        """
        # 基本条件：必须是同一类型的报文
        if request.packet_type != response.packet_type:
            return -1

        # 基本条件：响应必须在请求之后
        if response.timestamp <= request.timestamp:
            return -1

        # 严格的时间限制：响应必须在请求后2秒内
        time_diff = response.timestamp - request.timestamp
        if time_diff > 2.0:
            return -1

        # 必须条件1：报文ID必须相同
        if request.packet_id != response.packet_id:
            return -1

        # 必须条件2：IP地址必须匹配（请求的源IP应该是响应的目标IP）
        if not (request.src_ip == response.dst_ip and request.dst_ip == response.src_ip):
            return -1

        # 确保属性已解析
        if request.parsed_packet is None:
            self.parser.parse_packet_attributes(request)
        if response.parsed_packet is None:
            self.parser.parse_packet_attributes(response)

        # 必须条件3：NAS-IP-Address必须相同
        req_nas_ip = None
        resp_nas_ip = None

        if (request.parsed_packet and 'NAS-IP-Address' in request.parsed_packet):
            req_nas_ip = request.parsed_packet['NAS-IP-Address'][0]
            if isinstance(req_nas_ip, bytes):
                req_nas_ip = req_nas_ip.decode('utf-8', errors='ignore')

        if (response.parsed_packet and 'NAS-IP-Address' in response.parsed_packet):
            resp_nas_ip = response.parsed_packet['NAS-IP-Address'][0]
            if isinstance(resp_nas_ip, bytes):
                resp_nas_ip = resp_nas_ip.decode('utf-8', errors='ignore')

        # 如果两个报文都有NAS-IP-Address，必须相同
        if req_nas_ip and resp_nas_ip:
            if req_nas_ip != resp_nas_ip:
                return -1

        # 计算匹配分数（所有必须条件都满足后）
        score = 1000  # 基础分数

        # 时间越近分数越高
        if time_diff < 0.1:  # 100毫秒内
            score += 100
        elif time_diff < 0.5:  # 500毫秒内
            score += 50
        elif time_diff < 1.0:  # 1秒内
            score += 20
        else:  # 1-2秒内
            score += 10

        # 会话ID匹配加分（可选）
        if (request.parsed_packet and response.parsed_packet and
            'Acct-Session-Id' in request.parsed_packet and
            'Acct-Session-Id' in response.parsed_packet):

            req_session = request.parsed_packet['Acct-Session-Id'][0]
            resp_session = response.parsed_packet['Acct-Session-Id'][0]
            if isinstance(req_session, bytes):
                req_session = req_session.decode('utf-8', errors='ignore')
            if isinstance(resp_session, bytes):
                resp_session = resp_session.decode('utf-8', errors='ignore')
            if req_session == resp_session:
                score += 50

        if self.debug_matching:
            print(f"    匹配详情: ID={request.packet_id}, 时间差={time_diff:.3f}s, NAS-IP={req_nas_ip}, 分数={score}")

        return score


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="RADIUS PCAP测试工具")
    parser.add_argument("pcap_files", nargs="+", help="PCAP文件路径")
    parser.add_argument("--server", default="127.0.0.1", help="RADIUS服务器IP (默认: 127.0.0.1)")
    parser.add_argument("--auth-port", type=int, default=1812, help="认证端口 (默认: 1812)")
    parser.add_argument("--acct-port", type=int, default=1813, help="计费端口 (默认: 1813)")
    parser.add_argument("--secret", default="huaweitest", help="RADIUS密钥 (默认: huaweitest)")
    parser.add_argument("--mode", type=int, choices=[1, 2, 3, 4], default=1,
                       help="运行模式: 1=发送不比对, 2=发送并比对, 3=确认后发送并比对, 4=按用户名过滤")
    parser.add_argument("--username", help="模式4使用的用户名")
    parser.add_argument("--dictionary", default="conf/dictionary", help="字典文件路径")
    parser.add_argument("--max-packets", type=int, help="最大解析报文数量（用于大文件测试）")
    parser.add_argument("--ignore-extra", action="store_true",
                       help="忽略服务器响应中的额外属性（如Reply-Message等）")
    parser.add_argument("--debug-matching", action="store_true",
                       help="显示请求-响应匹配的调试信息")
    
    args = parser.parse_args()
    
    # 创建测试器
    tester = RadiusPcapTester(
        server_ip=args.server,
        auth_port=args.auth_port,
        acct_port=args.acct_port,
        secret=args.secret.encode(),
        dictionary_path=args.dictionary,
        ignore_extra_attributes=args.ignore_extra,
        debug_matching=args.debug_matching
    )
    
    # 加载PCAP文件
    packets = tester.load_pcap_files(args.pcap_files, args.max_packets)
    if not packets:
        print("没有找到RADIUS报文，退出")
        sys.exit(1)
    
    # 显示报文摘要
    tester.parser.print_packet_summary()
    
    # 根据模式执行测试
    if args.mode == 1:
        tester.mode1_send_all_no_compare(packets)
    elif args.mode == 2:
        tester.mode2_send_all_with_compare(packets)
    elif args.mode == 3:
        tester.mode3_send_with_confirmation(packets)
    elif args.mode == 4:
        if not args.username:
            print("模式4需要指定用户名，请使用 --username 参数")
            sys.exit(1)
        tester.mode4_send_by_username(packets, args.username)
    else:
        print(f"未知模式: {args.mode}")
