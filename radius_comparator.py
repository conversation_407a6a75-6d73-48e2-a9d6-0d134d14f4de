#!/usr/bin/env python3
"""
RADIUS响应比对模块
用于比较RADIUS服务器响应与抓包文件中的原始响应
"""

from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
import pyrad.packet
from pcap_parser import RadiusPacketInfo


@dataclass
class ComparisonResult:
    """比对结果"""
    is_match: bool
    differences: List[str]
    original_code: Optional[int] = None
    server_code: Optional[int] = None
    original_attributes: Optional[Dict] = None
    server_attributes: Optional[Dict] = None


class RadiusComparator:
    """RADIUS响应比对器"""
    
    def __init__(self):
        """初始化比对器"""
        self.ignored_attributes = {
            'Message-Authenticator',  # 消息认证器通常不同
            'State',                  # 状态属性可能不同
        }
        
        # RADIUS响应代码映射
        self.response_codes = {
            1: 'Access-Request',
            2: 'Access-Accept', 
            3: 'Access-Reject',
            4: 'Accounting-Request',
            5: 'Accounting-Response',
            11: 'Access-Challenge',
            12: 'Status-Server',
            13: 'Status-Client',
            40: 'Disconnect-Request',
            41: 'Disconnect-ACK',
            42: 'Disconnect-NAK',
            43: 'CoA-Request',
            44: 'CoA-ACK',
            45: 'CoA-NAK',
        }
    
    def compare_responses(self, 
                         original_response: RadiusPacketInfo,
                         server_response: pyrad.packet.Packet) -> ComparisonResult:
        """
        比较原始响应和服务器响应
        
        Args:
            original_response: 抓包文件中的原始响应
            server_response: 服务器返回的响应
            
        Returns:
            比对结果
        """
        differences = []
        
        # 比较响应代码
        original_code = original_response.packet_code
        server_code = server_response.code
        
        if original_code != server_code:
            original_name = self.response_codes.get(original_code, f"Unknown({original_code})")
            server_name = self.response_codes.get(server_code, f"Unknown({server_code})")
            differences.append(f"响应代码不匹配: 原始={original_name}, 服务器={server_name}")
        
        # 比较属性
        original_attrs = self._extract_attributes(original_response.parsed_packet)
        server_attrs = self._extract_attributes(server_response)
        
        attr_differences = self._compare_attributes(original_attrs, server_attrs)
        differences.extend(attr_differences)
        
        return ComparisonResult(
            is_match=(len(differences) == 0),
            differences=differences,
            original_code=original_code,
            server_code=server_code,
            original_attributes=original_attrs,
            server_attributes=server_attrs
        )
    
    def _extract_attributes(self, packet: Optional[pyrad.packet.Packet]) -> Dict[str, Any]:
        """
        提取RADIUS报文属性
        
        Args:
            packet: RADIUS报文对象
            
        Returns:
            属性字典
        """
        if not packet:
            return {}
            
        attributes = {}
        try:
            for attr_name in packet.keys():
                if attr_name not in self.ignored_attributes:
                    attr_values = packet[attr_name]
                    # 如果只有一个值，直接存储；否则存储列表
                    if len(attr_values) == 1:
                        attributes[attr_name] = attr_values[0]
                    else:
                        attributes[attr_name] = attr_values
        except Exception as e:
            print(f"提取属性时出错: {e}")
            
        return attributes
    
    def _compare_attributes(self, 
                           original_attrs: Dict[str, Any], 
                           server_attrs: Dict[str, Any]) -> List[str]:
        """
        比较属性
        
        Args:
            original_attrs: 原始属性
            server_attrs: 服务器属性
            
        Returns:
            差异列表
        """
        differences = []
        
        # 检查原始响应中存在但服务器响应中缺失的属性
        for attr_name, original_value in original_attrs.items():
            if attr_name not in server_attrs:
                differences.append(f"缺失属性: {attr_name} = {original_value}")
            elif server_attrs[attr_name] != original_value:
                differences.append(
                    f"属性值不匹配: {attr_name} "
                    f"(原始={original_value}, 服务器={server_attrs[attr_name]})"
                )
        
        # 检查服务器响应中存在但原始响应中没有的属性
        for attr_name, server_value in server_attrs.items():
            if attr_name not in original_attrs:
                differences.append(f"额外属性: {attr_name} = {server_value}")
        
        return differences
    
    def print_comparison_result(self, 
                               result: ComparisonResult, 
                               request_info: Optional[RadiusPacketInfo] = None):
        """
        打印比对结果
        
        Args:
            result: 比对结果
            request_info: 请求信息（可选）
        """
        print("\n" + "="*60)
        
        if request_info:
            print(f"请求信息:")
            print(f"  用户名: {request_info.username or 'N/A'}")
            print(f"  源IP: {request_info.src_ip}")
            print(f"  目标IP: {request_info.dst_ip}")
            print(f"  报文类型: {request_info.packet_type}")
            print(f"  报文ID: {request_info.packet_id}")
        
        print(f"\n比对结果: {'✓ 匹配' if result.is_match else '✗ 不匹配'}")
        
        if result.original_code is not None and result.server_code is not None:
            original_name = self.response_codes.get(result.original_code, f"Unknown({result.original_code})")
            server_name = self.response_codes.get(result.server_code, f"Unknown({result.server_code})")
            print(f"响应代码: 原始={original_name}, 服务器={server_name}")
        
        if result.differences:
            print(f"\n发现 {len(result.differences)} 个差异:")
            for i, diff in enumerate(result.differences, 1):
                print(f"  {i}. {diff}")
        
        # 显示属性详情
        if result.original_attributes or result.server_attributes:
            print(f"\n属性详情:")
            
            if result.original_attributes:
                print(f"  原始响应属性 ({len(result.original_attributes)} 个):")
                for attr_name, attr_value in sorted(result.original_attributes.items()):
                    print(f"    {attr_name}: {attr_value}")
            
            if result.server_attributes:
                print(f"  服务器响应属性 ({len(result.server_attributes)} 个):")
                for attr_name, attr_value in sorted(result.server_attributes.items()):
                    print(f"    {attr_name}: {attr_value}")
        
        print("="*60)
    
    def generate_summary_report(self, results: List[Tuple[RadiusPacketInfo, ComparisonResult]]):
        """
        生成汇总报告
        
        Args:
            results: 比对结果列表
        """
        if not results:
            print("没有比对结果")
            return
        
        total_count = len(results)
        match_count = sum(1 for _, result in results if result.is_match)
        mismatch_count = total_count - match_count
        
        print(f"\n{'='*60}")
        print(f"比对汇总报告")
        print(f"{'='*60}")
        print(f"总测试数: {total_count}")
        print(f"匹配数: {match_count} ({match_count/total_count*100:.1f}%)")
        print(f"不匹配数: {mismatch_count} ({mismatch_count/total_count*100:.1f}%)")
        
        if mismatch_count > 0:
            print(f"\n不匹配详情:")
            
            # 统计不匹配类型
            code_mismatches = 0
            attr_mismatches = 0
            
            for request_info, result in results:
                if not result.is_match:
                    if result.original_code != result.server_code:
                        code_mismatches += 1
                    if any("属性" in diff for diff in result.differences):
                        attr_mismatches += 1
            
            print(f"  响应代码不匹配: {code_mismatches}")
            print(f"  属性不匹配: {attr_mismatches}")
            
            # 显示前几个不匹配的案例
            print(f"\n前5个不匹配案例:")
            mismatch_cases = [(req, res) for req, res in results if not res.is_match]
            for i, (request_info, result) in enumerate(mismatch_cases[:5], 1):
                print(f"  {i}. 用户: {request_info.username or 'N/A'}, "
                      f"差异数: {len(result.differences)}")
                if result.differences:
                    print(f"     主要差异: {result.differences[0]}")
        
        print(f"{'='*60}")


if __name__ == "__main__":
    # 测试代码
    comparator = RadiusComparator()
    print("RADIUS响应比对器测试")
