#!/usr/bin/env python3
"""
性能测试脚本
比较优化前后的解析速度
"""

import time
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, '.')

from pcap_parser import RadiusPcapParser


def test_parsing_performance(pcap_file: str):
    """测试解析性能"""
    
    if not Path(pcap_file).exists():
        print(f"PCAP文件不存在: {pcap_file}")
        return
    
    print(f"测试文件: {pcap_file}")
    print("=" * 60)
    
    # 测试快速模式（只解析头部）
    print("\n1. 快速模式测试（只解析报文头部）")
    parser1 = RadiusPcapParser("conf/dictionary")
    
    start_time = time.time()
    packets_fast = parser1.parse_pcap_file(pcap_file, max_packets=1000, fast_mode=True)
    fast_time = time.time() - start_time
    
    print(f"快速模式耗时: {fast_time:.2f} 秒")
    print(f"解析报文数: {len(packets_fast)}")
    if packets_fast:
        print(f"平均每个报文: {fast_time/len(packets_fast)*1000:.2f} 毫秒")
    
    # 测试完整模式（解析所有属性）
    print("\n2. 完整模式测试（解析所有属性）")
    parser2 = RadiusPcapParser("conf/dictionary")
    
    start_time = time.time()
    packets_full = parser2.parse_pcap_file(pcap_file, max_packets=1000, fast_mode=False)
    full_time = time.time() - start_time
    
    print(f"完整模式耗时: {full_time:.2f} 秒")
    print(f"解析报文数: {len(packets_full)}")
    if packets_full:
        print(f"平均每个报文: {full_time/len(packets_full)*1000:.2f} 毫秒")
    
    # 性能对比
    if fast_time > 0 and full_time > 0:
        speedup = full_time / fast_time
        print(f"\n3. 性能对比")
        print(f"快速模式比完整模式快 {speedup:.1f} 倍")
        print(f"时间节省: {((full_time - fast_time) / full_time * 100):.1f}%")
    
    # 测试延迟解析
    if packets_fast:
        print(f"\n4. 延迟解析测试")
        test_packet = packets_fast[0]
        
        print(f"解析前 - 是否有属性: {test_packet.parsed_packet is not None}")
        
        start_time = time.time()
        parser1.parse_packet_attributes(test_packet)
        lazy_time = time.time() - start_time
        
        print(f"延迟解析耗时: {lazy_time*1000:.2f} 毫秒")
        print(f"解析后 - 是否有属性: {test_packet.parsed_packet is not None}")
        
        if test_packet.parsed_packet:
            print(f"属性数量: {len(test_packet.parsed_packet.keys())}")
    
    print("\n" + "=" * 60)


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python test_performance.py <pcap_file>")
        print("示例: python test_performance.py capfiles/his_6.pcap")
        return
    
    pcap_file = sys.argv[1]
    test_parsing_performance(pcap_file)


if __name__ == "__main__":
    main()
