#!/usr/bin/env python3
"""
异步RADIUS服务器性能测试脚本
"""

import asyncio
import socket
import time
import logging
import statistics
from pyrad import dictionary, packet

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AsyncRadiusClient:
    """异步RADIUS客户端"""
    
    def __init__(self, server_host='127.0.0.1', auth_port=1812, secret=b'testing123'):
        self.server_host = server_host
        self.auth_port = auth_port
        self.secret = secret
        self.dict = dictionary.Dictionary("conf/dictionary")
        
    async def send_auth_request(self, username, password):
        """发送认证请求"""
        # 创建认证包
        pkt = packet.AuthPacket(dict=self.dict, secret=self.secret)
        pkt.code = packet.AccessRequest
        pkt['User-Name'] = username
        pkt['User-Password'] = pkt.PwCrypt(password)
        pkt['NAS-IP-Address'] = '***********'
        pkt['NAS-Port'] = 1234
        
        # 发送UDP包
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        try:
            start_time = time.time()
            sock.sendto(pkt.RequestPacket(), (self.server_host, self.auth_port))
            
            # 接收回复
            sock.settimeout(5.0)
            data, addr = sock.recvfrom(4096)
            end_time = time.time()
            
            # 解析回复
            reply = packet.AuthPacket(packet=data, dict=self.dict, secret=self.secret)
            
            return {
                'success': True,
                'response_time': end_time - start_time,
                'code': reply.code,
                'username': username
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'username': username
            }
        finally:
            sock.close()

async def performance_test(concurrent_users=100, requests_per_user=10):
    """性能测试"""
    logger.info(f"Starting performance test: {concurrent_users} concurrent users, {requests_per_user} requests each")
    
    client = AsyncRadiusClient()
    
    async def user_session(user_id):
        """模拟用户会话"""
        results = []
        for i in range(requests_per_user):
            username = "<EMAIL>"
            password = "testpass"
            
            result = await client.send_auth_request(username, password)
            results.append(result)
            
            # 小延迟避免过于密集的请求
            # await asyncio.sleep(0.01)
        
        return results
    
    # 创建并发任务
    start_time = time.time()
    tasks = [user_session(i) for i in range(concurrent_users)]
    
    # 执行所有任务
    all_results = await asyncio.gather(*tasks, return_exceptions=True)
    end_time = time.time()
    
    # 统计结果
    total_requests = 0
    successful_requests = 0
    failed_requests = 0
    response_times = []
    
    for user_results in all_results:
        if isinstance(user_results, Exception):
            logger.error(f"User session failed: {user_results}")
            continue
            
        for result in user_results:
            total_requests += 1
            if result['success']:
                successful_requests += 1
                response_times.append(result['response_time'])
            else:
                failed_requests += 1
                logger.warning(f"Request failed for {result['username']}: {result.get('error', 'Unknown error')}")
    
    # 计算统计信息
    total_time = end_time - start_time
    requests_per_second = total_requests / total_time if total_time > 0 else 0
    
    logger.info("=" * 60)
    logger.info("PERFORMANCE TEST RESULTS")
    logger.info("=" * 60)
    logger.info(f"Total time: {total_time:.2f} seconds")
    logger.info(f"Total requests: {total_requests}")
    logger.info(f"Successful requests: {successful_requests}")
    logger.info(f"Failed requests: {failed_requests}")
    logger.info(f"Success rate: {(successful_requests/total_requests*100):.2f}%")
    logger.info(f"Requests per second: {requests_per_second:.2f}")
    
    if response_times:
        logger.info(f"Average response time: {statistics.mean(response_times)*1000:.2f} ms")
        logger.info(f"Median response time: {statistics.median(response_times)*1000:.2f} ms")
        logger.info(f"Min response time: {min(response_times)*1000:.2f} ms")
        logger.info(f"Max response time: {max(response_times)*1000:.2f} ms")
        logger.info(f"95th percentile: {statistics.quantiles(response_times, n=20)[18]*1000:.2f} ms")
    
    logger.info("=" * 60)

async def stress_test():
    """压力测试 - 逐步增加并发数"""
    logger.info("Starting stress test...")
    
    test_scenarios = [
        # (10, 5),    # 10个并发用户，每人5个请求
        # (50, 5),    # 50个并发用户，每人5个请求
        # (100, 5),   # 100个并发用户，每人5个请求
        # (200, 5),   # 200个并发用户，每人5个请求
        # (500, 3),   # 500个并发用户，每人3个请求
        (1000, 5),  # 1000个并发用户，每人2个请求
    ]
    
    for concurrent_users, requests_per_user in test_scenarios:
        logger.info(f"\n--- Testing {concurrent_users} concurrent users ---")
        await performance_test(concurrent_users, requests_per_user)
        await asyncio.sleep(2)  # 测试间隔

async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Async RADIUS Server Performance Test')
    parser.add_argument('--concurrent', type=int, default=100, help='Number of concurrent users')
    parser.add_argument('--requests', type=int, default=10, help='Requests per user')
    parser.add_argument('--stress', action='store_true', help='Run stress test')
    
    args = parser.parse_args()
    
    if args.stress:
        await stress_test()
    else:
        await performance_test(args.concurrent, args.requests)

if __name__ == '__main__':
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
